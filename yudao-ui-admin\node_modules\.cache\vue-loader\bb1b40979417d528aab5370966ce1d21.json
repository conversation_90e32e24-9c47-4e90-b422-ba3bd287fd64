{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\wandaWorkOrder\\index.vue?vue&type=style&index=0&id=5becade4&scoped=true&lang=css&", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\wandaWorkOrder\\index.vue", "mtime": 1753858477011}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\css-loader@3.6.0_webpack@4.46.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1667694382297}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1667694382639}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1667694382634}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoucHJvZ3Jlc3MtY29udGVudCB7DQogIHBhZGRpbmc6IDIwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCi5wcm9ncmVzcy10ZXh0IHsNCiAgbWFyZ2luLXRvcDogMTBweDsNCiAgY29sb3I6ICM2MDYyNjY7DQp9DQouZXJyb3ItbWVzc2FnZSB7DQogIGNvbG9yOiAjRjU2QzZDOw0KICBtYXJnaW4tdG9wOiAxMHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoucHJvZ3Jlc3MtaW5mbyB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoudGltZS1yYW5nZS1pbmZvIHsNCiAgbWFyZ2luLXRvcDogMTBweDsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCi5lbC1kZXNjcmlwdGlvbnMgew0KICBtYXJnaW46IDIwcHg7DQp9DQouZWwtZGVzY3JpcHRpb25zLWl0ZW1fX2NvbnRlbnQgew0KICBsaW5lLWhlaWdodDogMjRweDsNCn0NCi5lbC1kZXNjcmlwdGlvbnMtaXRlbV9fY29udGVudCBkaXYgew0KICBtYXJnaW46IDhweCAwOw0KfQ0KLnN0YXR1cy1tZXNzYWdlIHsNCiAgbWFyZ2luLXRvcDogNXB4Ow0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KfQ0KLnByb2dyZXNzLXRleHQgew0KICBkaXNwbGF5OiBibG9jazsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBtYXJnaW4tdG9wOiA1cHg7DQp9DQouc3RhdHVzLW1lc3NhZ2Ugew0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBtYXJnaW4tdG9wOiA1cHg7DQogIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KfQ0KLmFjdGlvbi1jb2x1bW4gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouYWN0aW9uLWNvbHVtbiAuZWwtYnV0dG9uIHsNCiAgbWFyZ2luLWxlZnQ6IDA7IC8qIOimhueblmVsZW1lbnQtdWnnmoTpu5jorqRtYXJnaW4gKi8NCiAgd2lkdGg6IDkwcHg7ICAgIC8qIOWbuuWumuWuveW6puehruS/neaMiemSrue7n+S4gCAqLw0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqhCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/insurance/wandaWorkOrder", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n\r\n    <!-- 搜索工具栏 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\r\n      <el-form-item label=\"医院代码\" prop=\"hospitalCode\">\r\n        <el-input v-model=\"queryParams.hospitalCode\" placeholder=\"请输入医院代码\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"医院名称\" prop=\"hospitalName\">\r\n        <el-input v-model=\"queryParams.hospitalName\" placeholder=\"请输入医院名称\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"工单类型\" prop=\"treatmentSerialNumberType\">\r\n        <el-select v-model=\"queryParams.treatmentSerialNumberType\" placeholder=\"请选择工单类型\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas(DICT_TYPE.INSURANCE_TREATMENT_TYPE)\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"就诊流水号\" prop=\"treatmentSerialNumber\">\r\n        <el-input v-model=\"queryParams.treatmentSerialNumber\" placeholder=\"请输入就诊流水号\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"姓名\" prop=\"name\">\r\n        <el-input v-model=\"queryParams.name\" placeholder=\"请输入姓名\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"身份证号码\" prop=\"idCardNumber\">\r\n        <el-input v-model=\"queryParams.idCardNumber\" placeholder=\"请输入身份证号码\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"移动电话\" prop=\"mobilePhoneNumber\">\r\n        <el-input v-model=\"queryParams.mobilePhoneNumber\" placeholder=\"请输入移动电话\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"科室名称\" prop=\"departmentName\">\r\n        <el-input v-model=\"queryParams.departmentName\" placeholder=\"请输入科室名称\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"医生姓名\" prop=\"doctorName\">\r\n        <el-input v-model=\"queryParams.doctorName\" placeholder=\"请输入医生姓名\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"主诊断编码\" prop=\"mainDiagnosisCode\">\r\n        <el-input v-model=\"queryParams.mainDiagnosisCode\" placeholder=\"请输入主诊断编码\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"就医时间\">\r\n        <el-date-picker v-model=\"dateRangeTreatmentDatetime\" style=\"width: 240px\" value-format=\"yyyy-MM-dd\"\r\n                        type=\"daterange\" range-separator=\"-\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"保险类型\" prop=\"orderType\">\r\n        <el-input v-model=\"queryParams.orderType\" placeholder=\"请输入保险类型\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"出院时间\">\r\n        <el-date-picker v-model=\"dateRangeLeaveHospitalDatetime\" style=\"width: 240px\" value-format=\"yyyy-MM-dd\"\r\n                        type=\"daterange\" range-separator=\"-\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker v-model=\"dateRangeCreateTime\" style=\"width: 240px\" value-format=\"yyyy-MM-dd\"\r\n                        type=\"daterange\" range-separator=\"-\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"工单状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择工单状态\" clearable>\r\n          <el-option label=\"未处理\" :value=\"0\" />\r\n          <el-option label=\"已生成工单\" :value=\"1\" />\r\n          <el-option label=\"生成失败\" :value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"失败类型\" prop=\"failType\" v-if=\"queryParams.status === 2\">\r\n        <el-select v-model=\"queryParams.failType\" placeholder=\"请选择失败类型\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas('wanda_order_fail_type')\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"parseInt(dict.value)\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作工具栏 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-refresh\" size=\"mini\" @click=\"handleSync\"\r\n                   v-hasPermi=\"['insurance:wanda-work-order:sync']\">同步预处理工单</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" :loading=\"exportLoading\"\r\n                   v-hasPermi=\"['insurance:wanda-work-order:export']\">导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"info\" plain icon=\"el-icon-s-data\" size=\"mini\" @click=\"handleStats\"\r\n                   v-hasPermi=\"['insurance:wanda-work-order:stats']\">字段完整统计</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleBatchGenerate\"\r\n          v-hasPermi=\"['insurance:wanda-work-order:generate']\">\r\n          批量生成\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-refresh\" size=\"mini\" @click=\"handleBatchRegenerate\"\r\n          v-hasPermi=\"['insurance:wanda-work-order:generate']\">\r\n          批量重新生成\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-document-add\" size=\"mini\" @click=\"handleBatchSupplementElectronicBill\"\r\n          v-hasPermi=\"['insurance:wanda-work-order:update']\">\r\n          补充电子票据信息\r\n        </el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"list\" size=\"mini\" :header-cell-style=\"{background:'#F5F7FA'}\">\r\n      <el-table-column label=\"编号\" align=\"center\" prop=\"id\" width=\"80\" />\r\n      <el-table-column label=\"医院代码\" align=\"center\" prop=\"hospitalCode\" width=\"120\" show-overflow-tooltip />\r\n      <el-table-column label=\"工单类型\" align=\"center\" prop=\"treatmentSerialNumberType\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_TREATMENT_TYPE\" :value=\"scope.row.treatmentSerialNumberType\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"就诊流水号\" align=\"center\" prop=\"treatmentSerialNumber\" width=\"120\" show-overflow-tooltip />\r\n      <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"100\" />\r\n      <el-table-column label=\"身份证号码\" align=\"center\" prop=\"idCardNumber\" width=\"180\" show-overflow-tooltip />\r\n      <el-table-column label=\"移动电话\" align=\"center\" prop=\"mobilePhoneNumber\" width=\"120\" show-overflow-tooltip />\r\n      <el-table-column label=\"科室名称\" align=\"center\" prop=\"departmentName\" width=\"120\" show-overflow-tooltip />\r\n      <el-table-column label=\"医生姓名\" align=\"center\" prop=\"doctorName\" width=\"100\" />\r\n      <el-table-column label=\"主诊断编码\" align=\"center\" prop=\"mainDiagnosisCode\" width=\"120\" show-overflow-tooltip />\r\n      <el-table-column label=\"就医时间\" align=\"center\" prop=\"treatmentDatetime\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.treatmentDatetime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"保险类型\" align=\"center\" prop=\"orderType\" width=\"100\" />\r\n      <el-table-column label=\"出院时间\" align=\"center\" prop=\"leaveHospitalDatetime\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.leaveHospitalDatetime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"电子票据号\" align=\"center\" prop=\"electronicBillIds\" width=\"120\" show-overflow-tooltip />\r\n      <el-table-column label=\"就诊类型\" align=\"center\" prop=\"treatmentType\" width=\"100\" />\r\n      <el-table-column label=\"当前状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.status === 0 ? '未处理' : scope.row.status === 1 ? '已生成工单' : '生成失败' }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column fixed=\"right\" label=\"操作\" align=\"center\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-view\" @click=\"handleView(scope.row)\"\r\n                     v-hasPermi=\"['insurance:wanda-work-order:query']\">查看详情</el-button>\r\n          <el-button v-if=\"scope.row.status !== 1\" size=\"mini\" type=\"text\" icon=\"el-icon-s-promotion\" @click=\"handleGenerateWorkOrder(scope.row)\"\r\n                     v-hasPermi=\"['insurance:wanda-work-order:generate']\">\r\n            {{scope.row.status === 2 ? '重新生成' : '生成工单'}}\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <!-- 分页组件 -->\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNo\" :limit.sync=\"queryParams.pageSize\"\r\n                @pagination=\"getList\"/>\r\n\r\n    <!-- 查看详情对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"医院信息\">\r\n          <div>医院代码：{{ form.hospitalCode }}</div>\r\n          <div>医院名称：{{ form.hospitalName }}</div>\r\n          <div>医院等级：{{ form.hospitalLevel }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"就诊信息\">\r\n          <div>工单类型：\r\n            <dict-tag :type=\"DICT_TYPE.INSURANCE_TREATMENT_TYPE\" :value=\"form.treatmentSerialNumberType\"/>\r\n          </div>\r\n          <div>就诊流水号：{{ form.treatmentSerialNumber }}</div>\r\n          <div>就诊时间：{{ parseTime(form.treatmentDatetime) }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"患者信息\">\r\n          <div>姓名：{{ form.name }}</div>\r\n          <div>身份证号：{{ form.idCardNumber }}</div>\r\n          <div>地址：{{ form.address }}</div>\r\n          <div>移动电话：{{ form.mobilePhoneNumber }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"就医科室\">\r\n          <div>科室编码：{{ form.departmentCode }}</div>\r\n          <div>科室名称：{{ form.departmentName }}</div>\r\n          <div>医生代码：{{ form.doctorCode }}</div>\r\n          <div>医生姓名：{{ form.doctorName }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"诊断信息\" :span=\"2\">\r\n          <div>主诊断编码：{{ form.mainDiagnosisCode }}</div>\r\n          <div>主诊断名称：{{ form.mainDiagnosisName }}</div>\r\n          <div>其他诊断编码：{{ form.otherDiagnosisCode }}</div>\r\n          <div>其他诊断名称：{{ form.otherDiagnosisName }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"病史信息\" :span=\"2\">\r\n          <div>主诉：{{ form.mainComplaint }}</div>\r\n          <div>现病史：{{ form.currentMedicalHistory }}</div>\r\n          <div>既往史：{{ form.pastMedicalHistory }}</div>\r\n          <div>遗传史：{{ form.geneticHistory }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"住院信息\" v-if=\"form.treatmentSerialNumberType === 1\">\r\n          <div>住院号：{{ form.hospitalizationNumber }}</div>\r\n          <div>病区：{{ form.inpatientArea }}</div>\r\n          <div>床号：{{ form.bedNumber }}</div>\r\n          <div>并发症代码：{{ form.complicationCode }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"住院时间信息\" v-if=\"form.treatmentSerialNumberType === 1\">\r\n          <div>住院时间：{{ parseTime(form.inHospitalDatetime) }}</div>\r\n          <div>出院时间：{{ parseTime(form.leaveHospitalDatetime) }}</div>\r\n          <div>出院状态：{{ form.leaveHospitalState }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"诊断记录\" :span=\"2\" v-if=\"form.treatmentSerialNumberType === 1\">\r\n          <div>入院诊断：{{ form.inHospitalDiagnosisName }}</div>\r\n          <div>出院诊断：{{ form.leaveHospitalDiagnosisName }}</div>\r\n          <div>出院描述：{{ form.leaveHospitalDescription }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"其他信息\" :span=\"2\">\r\n          <div>电子票据号：{{ form.electronicBillIds }}</div>\r\n          <div>就诊类型：{{ form.treatmentType }}</div>\r\n          <div>保险类型：{{ form.orderType }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item v-if=\"form.status === 2\" label=\"失败信息\" :span=\"2\">\r\n          <div>\r\n            <span style=\"font-weight: bold;\">失败类型：</span>\r\n            <dict-tag type=\"wanda_order_fail_type\" :value=\"form.failType\"/>\r\n          </div>\r\n          <div style=\"color: #F56C6C; margin-top: 5px;\">\r\n            <span style=\"font-weight: bold;\">失败原因：</span>{{ form.failReason }}\r\n          </div>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"open = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加同步对话框 -->\r\n    <el-dialog title=\"同步预处理工单\" :visible.sync=\"syncDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"syncForm\" :model=\"syncForm\" label-width=\"100px\">\r\n        <el-form-item label=\"时间范围\" required>\r\n          <el-date-picker\r\n            v-model=\"syncForm.dateRange\"\r\n            type=\"datetimerange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始时间\"\r\n            end-placeholder=\"结束时间\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"pickerOptions\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"syncDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitSync\" :loading=\"syncLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加进度对话框 -->\r\n    <el-dialog title=\"同步进度\" :visible.sync=\"progressDialogVisible\" width=\"400px\" \r\n      :close-on-click-modal=\"false\" @closed=\"closeProgressDialog\">\r\n      <div class=\"progress-content\">\r\n        <el-progress :percentage=\"syncProgress\" :status=\"syncStatus\"></el-progress>\r\n        <div class=\"progress-text\">{{progressText}}</div>\r\n        <div v-if=\"syncErrorMessage\" class=\"error-message\">{{syncErrorMessage}}</div>\r\n        <div class=\"progress-info\" v-if=\"syncCurrentPage && syncTotalPages\">\r\n          当前进度: {{syncCurrentPage}}/{{syncTotalPages}}页\r\n        </div>\r\n        <div class=\"time-range-info\" v-if=\"timeRange.start && timeRange.end\">\r\n          同步时间: {{timeRange.start}} 至 {{timeRange.end}}\r\n        </div>\r\n        <el-button \r\n          v-if=\"canResume\" \r\n          type=\"primary\" \r\n          @click=\"handleResume\"\r\n          :loading=\"resumeLoading\"\r\n          style=\"margin-top: 15px\">\r\n          继续同步\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 字段完整统计对话框 -->\r\n    <el-dialog title=\"字段完整统计\" :visible.sync=\"statsDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-table :data=\"statsData\" border style=\"width: 100%\">\r\n        <el-table-column prop=\"fieldName\" label=\"字段名称\" width=\"180\"/>\r\n        <el-table-column prop=\"validCount\" label=\"完整数据数量\"/>\r\n        <el-table-column prop=\"percentage\" label=\"完整度\">\r\n          <template slot-scope=\"scope\">\r\n            <el-progress :percentage=\"scope.row.percentage\"></el-progress>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div style=\"margin-top: 20px\">\r\n        <strong>数据总数：{{ totalCount }}</strong>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"statsDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 未完成任务列表对话框 -->\r\n    <el-dialog title=\"未完成的同步任务\" :visible.sync=\"unfinishedTasksDialogVisible\" width=\"800px\" append-to-body>\r\n      <el-table v-loading=\"unfinishedTasksLoading\" :data=\"unfinishedTasks\" border style=\"width: 100%\" \r\n               :header-cell-style=\"{background:'#F5F7FA'}\">\r\n        <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"180\" align=\"center\"/>\r\n        <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"180\" align=\"center\"/>\r\n        <el-table-column label=\"同步进度\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-progress :percentage=\"scope.row.progress\" \r\n                          :status=\"scope.row.status === SYNC_TASK_STATUS.RUNNING ? '' : 'exception'\">\r\n            </el-progress>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"状态信息\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tooltip placement=\"top\" :disabled=\"!scope.row.errorMessage\">\r\n              <div slot=\"content\" v-if=\"scope.row.errorMessage\">\r\n                <div style=\"max-width: 300px; word-break: break-all;\">{{ scope.row.errorMessage }}</div>\r\n              </div>\r\n              <div>\r\n                <el-tag :type=\"getStatusType(scope.row.status)\" size=\"mini\">\r\n                  {{ getStatusText(scope.row.status) }}\r\n                </el-tag>\r\n                <div class=\"status-message\">\r\n                  {{ getShortMessage(scope.row.message) }}\r\n                </div>\r\n              </div>\r\n            </el-tooltip>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"180\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-column\">\r\n              <el-button \r\n                type=\"primary\" \r\n                size=\"mini\" \r\n                v-if=\"!isTaskLocked(scope.row.taskId)\"\r\n                @click=\"selectUnfinishedTask(scope.row)\">\r\n                继续同步\r\n              </el-button>\r\n              <el-button \r\n                v-if=\"isTaskLocked(scope.row.taskId)\"\r\n                type=\"info\" \r\n                size=\"mini\" \r\n                @click=\"viewTaskDetail(scope.row)\"\r\n                style=\"margin-top: 5px\">\r\n                查看详情\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"unfinishedTasksDialogVisible = false\">关 闭</el-button>\r\n        <el-button type=\"primary\" @click=\"refreshUnfinishedTasks\">刷 新</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量生成进度对话框 -->\r\n    <el-dialog :title=\"batchTitle\" :visible.sync=\"batchOpen\" width=\"500px\" append-to-body>\r\n      <div v-if=\"batchProgress\">\r\n        <el-progress :percentage=\"batchProgress.progress\" :status=\"getProgressStatus(batchProgress)\"></el-progress>\r\n        <div class=\"progress-info\">\r\n          <p>总数: {{ batchProgress.totalCount }}</p>\r\n          <p>已处理: {{ batchProgress.processedCount }}</p>\r\n          <p>成功: {{ batchProgress.successCount }}</p>\r\n          <p>失败: {{ batchProgress.failCount }}</p>\r\n          <p>状态: {{ batchProgress.message }}</p>\r\n          <p v-if=\"batchProgress.errorMessage\" class=\"error-message\">错误: {{ batchProgress.errorMessage }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"batchOpen = false\">关 闭</el-button>\r\n        <el-button type=\"primary\" @click=\"handleDownloadReport\" v-if=\"canDownloadReport\">下载报告</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  createWandaWorkOrder,\r\n  updateWandaWorkOrder,\r\n  deleteWandaWorkOrder,\r\n  getWandaWorkOrder,\r\n  getWandaWorkOrderPage,\r\n  exportWandaWorkOrderExcel,\r\n  startSync,\r\n  getSyncProgress,\r\n  resumeSync,\r\n  generateWorkOrder,\r\n  getUnfinishedTaskList,\r\n  checkTaskLock,\r\n  batchGenerateWorkOrder,\r\n  getBatchGenerateProgress,\r\n  downloadBatchGenerateReport,\r\n  batchRegenerateFailedWorkOrder,\r\n  getUnfinishedBatchTasks,\r\n  batchSupplementElectronicBill,\r\n  getBatchSupplementProgress\r\n} from \"@/api/insurance/wandaWorkOrder\";\r\nimport Editor from '@/components/Editor';\r\nimport { getWandaWorkOrderStats } from \"@/api/insurance/wandaWorkOrder\";\r\n\r\n// 添加同步任务状态常量\r\nconst SYNC_TASK_STATUS = {\r\n  RUNNING: 'RUNNING',\r\n  PAUSED: 'PAUSED',\r\n  COMPLETED: 'COMPLETED',\r\n  FAILED: 'FAILED'\r\n};\r\n\r\nexport default {\r\n  name: \"WandaWorkOrder\",\r\n  components: {\r\n    Editor,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 导出遮罩层\r\n      exportLoading: false,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 预处理工单列表\r\n      list: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      dateRangeTreatmentDatetime: [],\r\n      dateRangeLeaveHospitalDatetime: [],\r\n      dateRangeCreateTime: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        hospitalCode: null,\r\n        hospitalName: null,\r\n        treatmentSerialNumberType: null,\r\n        treatmentSerialNumber: null,\r\n        name: null,\r\n        idCardNumber: null,\r\n        mobilePhoneNumber: null,\r\n        departmentName: null,\r\n        doctorName: null,\r\n        mainDiagnosisCode: null,\r\n        orderType: null,\r\n        electronicBillIds: null,\r\n        status: null,\r\n        failType: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      },\r\n      // 同步对话框\r\n      syncDialogVisible: false,\r\n      syncForm: {\r\n        dateRange: [],\r\n      },\r\n      syncLoading: false,\r\n      syncProgress: 0,\r\n      syncStatus: '',\r\n      progressText: '',\r\n      progressDialogVisible: false,\r\n      pickerOptions: {\r\n        shortcuts: [{\r\n          text: '最近一周',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近一个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }]\r\n      },\r\n      canResume: false,\r\n      resumeLoading: false,\r\n      syncCurrentPage: 0,\r\n      syncTotalPages: 0,\r\n      syncErrorMessage: '',\r\n      currentTaskId: null,\r\n      timeRange: {\r\n        start: '',\r\n        end: ''\r\n      },\r\n      statsDialogVisible: false,\r\n      statsData: [],\r\n      totalCount: 0,\r\n      unfinishedTasksDialogVisible: false,\r\n      unfinishedTasksLoading: false,\r\n      unfinishedTasks: [],\r\n      lockedTaskIds: [],\r\n      // 添加SYNC_TASK_STATUS到data中，这样模板可以访问它\r\n      SYNC_TASK_STATUS: SYNC_TASK_STATUS,\r\n      batchOpen: false,\r\n      batchTitle: '',\r\n      batchProgress: null,\r\n      batchTaskId: null,\r\n      progressTimer: null,\r\n    };\r\n  },\r\n  computed: {\r\n    canDownloadReport() {\r\n      return this.batchProgress && \r\n             (this.batchProgress.status === 'COMPLETED' || this.batchProgress.status === 'FAILED') && \r\n             this.batchProgress.processedCount > 0;\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.checkUnfinishedTask();\r\n  },\r\n  methods: {\r\n    /** 查询列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      this.addBeginAndEndTime(params, this.dateRangeTreatmentDatetime, 'treatmentDatetime');\r\n      this.addBeginAndEndTime(params, this.dateRangeLeaveHospitalDatetime, 'leaveHospitalDatetime');\r\n      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime');\r\n      // 执行查询\r\n      getWandaWorkOrderPage(params).then(response => {\r\n        this.list = response.data.list;\r\n        this.total = response.data.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: undefined,\r\n        hospitalCode: undefined,\r\n        hospitalName: undefined,\r\n        hospitalLevel: undefined,\r\n        treatmentSerialNumberType: undefined,\r\n        treatmentSerialNumber: undefined,\r\n        name: undefined,\r\n        idCardNumber: undefined,\r\n        address: undefined,\r\n        mobilePhoneNumber: undefined,\r\n        departmentCode: undefined,\r\n        departmentName: undefined,\r\n        doctorCode: undefined,\r\n        doctorName: undefined,\r\n        mainDiagnosisCode: undefined,\r\n        mainDiagnosisName: undefined,\r\n        otherDiagnosisCode: undefined,\r\n        otherDiagnosisName: undefined,\r\n        treatmentDatetime: undefined,\r\n        mainComplaint: undefined,\r\n        currentMedicalHistory: undefined,\r\n        pastMedicalHistory: undefined,\r\n        geneticHistory: undefined,\r\n        orderType: undefined,\r\n        status: undefined,\r\n        hospitalizationNumber: undefined,\r\n        contactPersonName: undefined,\r\n        contactPersonPhoneNumber: undefined,\r\n        inpatientArea: undefined,\r\n        bedNumber: undefined,\r\n        complicationCode: undefined,\r\n        inHospitalDatetime: undefined,\r\n        leaveHospitalDatetime: undefined,\r\n        leaveHospitalState: undefined,\r\n        leaveHospitalDiagnosisName: undefined,\r\n        inHospitalDiagnosisName: undefined,\r\n        leaveHospitalDescription: undefined,\r\n        electronicBillIds: undefined,\r\n        treatmentType: undefined,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNo = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRangeTreatmentDatetime = [];\r\n      this.dateRangeLeaveHospitalDatetime = [];\r\n      this.dateRangeCreateTime = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 查看按钮操作 */\r\n    handleView(row) {\r\n      this.reset();\r\n      const id = row.id;\r\n      getWandaWorkOrder(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"查看预处理工单\";\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      params.pageNo = undefined;\r\n      params.pageSize = undefined;\r\n      this.addBeginAndEndTime(params, this.dateRangeTreatmentDatetime, 'treatmentDatetime');\r\n      this.addBeginAndEndTime(params, this.dateRangeLeaveHospitalDatetime, 'leaveHospitalDatetime');\r\n      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime');\r\n      // 执行导出\r\n      this.$modal.confirm('是否确认导出所有预处理工单数据项?').then(() => {\r\n          this.exportLoading = true;\r\n          return exportWandaWorkOrderExcel(params);\r\n        }).then(response => {\r\n          this.$download.excel(response, '预处理工单.xls');\r\n          this.exportLoading = false;\r\n        }).catch(() => {});\r\n    },\r\n    /** 打开未完成任务列表对话框时，重置锁定状态 */\r\n    checkUnfinishedTask() {\r\n      // 清空锁定任务状态\r\n      this.lockedTaskIds = [];\r\n      getUnfinishedTaskList().then(response => {\r\n        if (response.data && response.data.length > 0) {\r\n          this.unfinishedTasks = response.data;\r\n          // 检查后端锁定状态\r\n          this.syncLockStatuses();\r\n          this.unfinishedTasksDialogVisible = true;\r\n        }\r\n      }).catch(error => {\r\n        this.unfinishedTasksLoading = false;\r\n        console.error(\"检查未完成任务异常:\", error);\r\n      });\r\n    },\r\n    /** 同步按钮操作 */\r\n    handleSync() {\r\n      this.unfinishedTasksLoading = true;\r\n      // 清空锁定任务状态\r\n      this.lockedTaskIds = [];\r\n      getUnfinishedTaskList().then(response => {\r\n        this.unfinishedTasksLoading = false;\r\n        if (response.data && response.data.length > 0) {\r\n          this.unfinishedTasks = response.data;\r\n          // 检查后端锁定状态\r\n          this.syncLockStatuses();\r\n          this.unfinishedTasksDialogVisible = true;\r\n        } else {\r\n          // 没有未完成的任务,打开同步对话框\r\n          this.syncDialogVisible = true;\r\n          this.syncForm.dateRange = [];\r\n        }\r\n      }).catch(error => {\r\n        this.unfinishedTasksLoading = false;\r\n        console.error(\"获取未完成任务异常:\", error);\r\n        // 出错时也打开同步对话框\r\n        this.syncDialogVisible = true;\r\n        this.syncForm.dateRange = [];\r\n      });\r\n    },\r\n    /** 检查后端锁定状态 */\r\n    syncLockStatuses() {\r\n      // 创建新的API接口来获取所有任务的锁定状态\r\n      // 由于现在没有批量检查锁状态的API，这里默认所有任务没有锁定\r\n      this.unfinishedTasks.forEach(task => {\r\n        // 为每个任务创建单独的锁检查请求\r\n        this.checkTaskLockStatus(task.taskId);\r\n      });\r\n    },\r\n    /** 检查单个任务锁状态 */\r\n    checkTaskLockStatus(taskId) {\r\n      checkTaskLock(taskId).then(response => {\r\n        const isLocked = response.data === true;\r\n        const index = this.lockedTaskIds.indexOf(taskId);\r\n        \r\n        if (isLocked && index === -1) {\r\n          // 添加到锁定列表\r\n          this.lockedTaskIds.push(taskId);\r\n        } else if (!isLocked && index > -1) {\r\n          // 从锁定列表中移除\r\n          this.lockedTaskIds.splice(index, 1);\r\n        }\r\n      }).catch(() => {\r\n        // 如果检查失败，假定任务未锁定\r\n        const index = this.lockedTaskIds.indexOf(taskId);\r\n        if (index > -1) {\r\n          this.lockedTaskIds.splice(index, 1);\r\n        }\r\n      });\r\n    },\r\n    /** 刷新未完成任务列表 */\r\n    refreshUnfinishedTasks() {\r\n      this.unfinishedTasksLoading = true;\r\n      getUnfinishedTaskList().then(response => {\r\n        this.unfinishedTasks = response.data || [];\r\n        this.unfinishedTasksLoading = false;\r\n        // 刷新锁状态\r\n        this.syncLockStatuses();\r\n      }).catch(() => {\r\n        this.unfinishedTasksLoading = false;\r\n      });\r\n    },\r\n    /** 关闭进度对话框时清理锁状态 */\r\n    closeProgressDialog() {\r\n      if (this.currentTaskId) {\r\n        // 当用户手动关闭进度对话框时，检查后端锁状态\r\n        this.checkTaskLockStatus(this.currentTaskId);\r\n      }\r\n      this.progressDialogVisible = false;\r\n    },\r\n    /** 提交同步 */\r\n    submitSync() {\r\n      if (!this.syncForm.dateRange || this.syncForm.dateRange.length !== 2) {\r\n        this.$message.error('请选择时间范围');\r\n        return;\r\n      }\r\n      \r\n      this.$modal.confirm('是否确认同步该时间范围内的预处理工单?').then(() => {\r\n        this.syncLoading = true;\r\n        const [startTime, endTime] = this.syncForm.dateRange;\r\n        startSync({ startTime, endTime }).then(response => {\r\n          this.syncDialogVisible = false;\r\n          this.syncLoading = false;\r\n          this.showProgress();\r\n          this.startProgressPolling(response.data);\r\n        }).catch(() => {\r\n          this.syncLoading = false;\r\n        });\r\n      });\r\n    },\r\n    /** 显示进度对话框 */\r\n    showProgress() {\r\n      this.progressDialogVisible = true;\r\n      this.syncProgress = 0;\r\n      this.syncStatus = '';\r\n      this.progressText = '正在同步...';\r\n      this.syncErrorMessage = '';\r\n      this.canResume = false;\r\n      this.syncCurrentPage = 0;\r\n      this.syncTotalPages = 0;\r\n      this.timeRange.start = '';\r\n      this.timeRange.end = '';\r\n    },\r\n    /** 开始轮询进度 */\r\n    startProgressPolling(taskId) {\r\n      this.currentTaskId = taskId;\r\n      localStorage.setItem('currentSyncTaskId', taskId);\r\n      \r\n      const poll = () => {\r\n        getSyncProgress(taskId).then(response => {\r\n          const { progress, status, message, canResume, currentPage, totalPages, errorMessage, startTime, endTime } = response.data;\r\n          this.syncProgress = progress;\r\n          this.progressText = message;\r\n          this.canResume = canResume;\r\n          this.syncCurrentPage = currentPage;\r\n          this.syncTotalPages = totalPages;\r\n          this.syncErrorMessage = errorMessage;\r\n          this.timeRange.start = startTime;\r\n          this.timeRange.end = endTime;\r\n          \r\n          if (status === 'COMPLETED') {\r\n            this.syncStatus = 'success';\r\n            this.progressText = '同步完成';\r\n            this.$message.success('同步完成');\r\n            this.getList(); // 刷新列表\r\n            setTimeout(() => {\r\n              this.progressDialogVisible = false;\r\n            }, 1500);\r\n          } else if (status === 'FAILED' || status === 'PAUSED') {\r\n            this.syncStatus = 'exception';\r\n            this.$message.error(errorMessage || '同步失败');\r\n          } else {\r\n            setTimeout(poll, 1000);\r\n          }\r\n        }).catch(() => {\r\n          this.syncStatus = 'exception';\r\n          this.progressText = '获取进度失败';\r\n        });\r\n      };\r\n      poll();\r\n    },\r\n    handleResume() {\r\n      this.resumeLoading = true;\r\n      // 标记任务为锁定状态\r\n      if (!this.lockedTaskIds.includes(this.currentTaskId)) {\r\n        this.lockedTaskIds.push(this.currentTaskId);\r\n      }\r\n      \r\n      resumeSync(this.currentTaskId).then(() => {\r\n        this.$message.success('已开始继续同步');\r\n        this.canResume = false;\r\n        // 重置进度状态\r\n        this.syncStatus = '';\r\n        this.syncProgress = 0;\r\n        this.progressText = '正在同步...';\r\n        this.syncErrorMessage = '';\r\n        // 重新开始轮询进度\r\n        this.startProgressPolling(this.currentTaskId);\r\n      }).catch(() => {\r\n        this.$message.error('继续同步失败');\r\n        // 失败时移除锁定标记\r\n        const index = this.lockedTaskIds.indexOf(this.currentTaskId);\r\n        if (index > -1) {\r\n          this.lockedTaskIds.splice(index, 1);\r\n        }\r\n      }).finally(() => {\r\n        this.resumeLoading = false;  // 无论成功失败都要关闭loading状态\r\n      });\r\n    },\r\n    /** 统计按钮操作 */\r\n    handleStats() {\r\n      this.statsDialogVisible = true;\r\n      getWandaWorkOrderStats().then(response => {\r\n        const data = response.data;\r\n        this.totalCount = data.totalCount;\r\n        this.statsData = [\r\n          { fieldName: '手机号', validCount: data.mobilePhoneNumberCount, percentage: Math.round(data.mobilePhoneNumberCount / data.totalCount * 100) },\r\n          { fieldName: '主诉', validCount: data.mainComplaintCount, percentage: Math.round(data.mainComplaintCount / data.totalCount * 100) },\r\n          { fieldName: '现病史', validCount: data.currentMedicalHistoryCount, percentage: Math.round(data.currentMedicalHistoryCount / data.totalCount * 100) },\r\n          { fieldName: '既往史', validCount: data.pastMedicalHistoryCount, percentage: Math.round(data.pastMedicalHistoryCount / data.totalCount * 100) },\r\n          { fieldName: '遗传史', validCount: data.geneticHistoryCount, percentage: Math.round(data.geneticHistoryCount / data.totalCount * 100) },\r\n          { fieldName: '主诊断编码', validCount: data.mainDiagnosisCodeCount, percentage: Math.round(data.mainDiagnosisCodeCount / data.totalCount * 100) },\r\n          { fieldName: '主诊断名称', validCount: data.mainDiagnosisNameCount, percentage: Math.round(data.mainDiagnosisNameCount / data.totalCount * 100) },\r\n          { fieldName: '电子票据号', validCount: data.electronicBillIdsCount, percentage: Math.round(data.electronicBillIdsCount / data.totalCount * 100) }\r\n        ];\r\n      });\r\n    },\r\n    /** 生成工单按钮操作 */\r\n    handleGenerateWorkOrder(row) {\r\n      this.$modal.confirm('是否确认生成工单?').then(() => {\r\n        return generateWorkOrder(row.id);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(\"生成成功\");\r\n        this.getList();\r\n      }).catch(() => {});\r\n    },\r\n    /** 选择未完成任务继续同步 */\r\n    selectUnfinishedTask(task) {\r\n      this.unfinishedTasksDialogVisible = false;\r\n      this.showProgress();\r\n      this.startProgressPolling(task.taskId);\r\n      // 标记任务为锁定状态\r\n      if (!this.lockedTaskIds.includes(task.taskId)) {\r\n        this.lockedTaskIds.push(task.taskId);\r\n      }\r\n    },\r\n    /** 判断任务是否被锁定 */\r\n    isTaskLocked(taskId) {\r\n      return this.lockedTaskIds.includes(taskId);\r\n    },\r\n    /** 获取状态标签类型 */\r\n    getStatusType(status) {\r\n      switch (status) {\r\n        case SYNC_TASK_STATUS.RUNNING: return 'success';\r\n        case SYNC_TASK_STATUS.PAUSED: return 'warning';\r\n        case SYNC_TASK_STATUS.FAILED: return 'danger';\r\n        case SYNC_TASK_STATUS.COMPLETED: return 'info';\r\n        default: return 'info';\r\n      }\r\n    },\r\n    /** 获取状态文本 */\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case SYNC_TASK_STATUS.RUNNING: return '同步中';\r\n        case SYNC_TASK_STATUS.PAUSED: return '已暂停';\r\n        case SYNC_TASK_STATUS.FAILED: return '同步失败';\r\n        case SYNC_TASK_STATUS.COMPLETED: return '已完成';\r\n        default: return '未知状态';\r\n      }\r\n    },\r\n    /** 获取简短消息 */\r\n    getShortMessage(message) {\r\n      if (!message) return '';\r\n      // 截取前30个字符，如果有更多则添加省略号\r\n      return message.length > 30 ? message.substring(0, 30) + '...' : message;\r\n    },\r\n    /** 查看任务详情 */\r\n    viewTaskDetail(task) {\r\n      this.currentTaskId = task.taskId;\r\n      this.timeRange = {\r\n        start: task.startTime,\r\n        end: task.endTime\r\n      };\r\n      \r\n      // 显示进度对话框\r\n      this.showProgress();\r\n      \r\n      // 开始轮询更新进度信息\r\n      this.startProgressPolling(task.taskId);\r\n    },\r\n    /** 批量生成按钮操作 */\r\n    handleBatchGenerate() {\r\n      this.batchTitle = '批量生成工单';\r\n      this.checkUnfinishedBatchTask();\r\n    },\r\n    /** 批量重新生成按钮操作 */\r\n    handleBatchRegenerate() {\r\n      this.batchTitle = '批量重新生成失败工单';\r\n      this.checkUnfinishedBatchTask();\r\n    },\r\n    /** 检查未完成的批量任务 */\r\n    checkUnfinishedBatchTask() {\r\n      getUnfinishedBatchTasks().then(response => {\r\n        if (response.data && response.data.length > 0) {\r\n          // 有未完成的任务,显示进度对话框\r\n          this.batchOpen = true;\r\n          this.batchProgress = response.data[0];\r\n          this.batchTaskId = response.data[0].taskId;\r\n          this.startBatchProgressPolling();\r\n        } else {\r\n          // 没有未完成的任务,开始新的批量处理\r\n          this.startBatchProcess();\r\n        }\r\n      }).catch(() => {\r\n        // 出错时也开始新的批量处理\r\n        this.startBatchProcess();\r\n      });\r\n    },\r\n    /** 开始批量处理 */\r\n    startBatchProcess() {\r\n      this.$confirm('确认要执行此操作吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.batchOpen = true;\r\n        this.batchProgress = null;\r\n        const processFunction = this.batchTitle === '批量生成工单' ? batchGenerateWorkOrder : batchRegenerateFailedWorkOrder;\r\n        processFunction(this.queryParams).then(response => {\r\n          this.batchTaskId = response.data;\r\n          this.startBatchProgressPolling();\r\n        }).catch(() => {\r\n          this.batchOpen = false;\r\n        });\r\n      });\r\n    },\r\n    /** 开始轮询进度 */\r\n    startBatchProgressPolling() {\r\n      if (this.progressTimer) {\r\n        clearInterval(this.progressTimer);\r\n      }\r\n      this.progressTimer = setInterval(() => {\r\n        this.queryProgress();\r\n      }, 1000);\r\n    },\r\n    /** 查询进度 */\r\n    queryProgress() {\r\n      if (!this.batchTaskId) return;\r\n      \r\n      getBatchGenerateProgress(this.batchTaskId).then(response => {\r\n        this.batchProgress = response.data;\r\n        if (this.batchProgress.status === 'COMPLETED' || this.batchProgress.status === 'FAILED') {\r\n          clearInterval(this.progressTimer);\r\n          this.progressTimer = null;\r\n        }\r\n      });\r\n    },\r\n    /** 获取进度条状态 */\r\n    getProgressStatus(progress) {\r\n      if (progress.status === 'FAILED') return 'exception';\r\n      if (progress.status === 'COMPLETED') return 'success';\r\n      return '';\r\n    },\r\n    /** 下载报告 */\r\n    handleDownloadReport() {\r\n      downloadBatchGenerateReport(this.batchTaskId).then(response => {\r\n        this.$download.excel(response, '批量生成工单报告.xlsx');\r\n      }).catch(() => {});\r\n    },\r\n    /** 补充电子票据信息按钮操作 */\r\n    handleBatchSupplementElectronicBill() {\r\n      this.$confirm('此操作将补充状态为待接单且票据完整度为无数据或不确定的工单的电子票据信息，确认要执行吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.batchTitle = '补充电子票据信息';\r\n        this.batchOpen = true;\r\n        this.batchProgress = null;\r\n\r\n        batchSupplementElectronicBill().then(response => {\r\n          this.batchTaskId = response.data;\r\n          this.startSupplementProgressPolling();\r\n        }).catch(() => {\r\n          this.batchOpen = false;\r\n        });\r\n      });\r\n    },\r\n    /** 开始轮询补充电子票据信息进度 */\r\n    startSupplementProgressPolling() {\r\n      if (this.progressTimer) {\r\n        clearInterval(this.progressTimer);\r\n      }\r\n      this.progressTimer = setInterval(() => {\r\n        this.querySupplementProgress();\r\n      }, 1000);\r\n    },\r\n    /** 查询补充电子票据信息进度 */\r\n    querySupplementProgress() {\r\n      if (!this.batchTaskId) return;\r\n\r\n      getBatchSupplementProgress(this.batchTaskId).then(response => {\r\n        this.batchProgress = response.data;\r\n        if (this.batchProgress.status === 'COMPLETED' || this.batchProgress.status === 'FAILED') {\r\n          clearInterval(this.progressTimer);\r\n          this.progressTimer = null;\r\n          // 任务完成后刷新列表\r\n          if (this.batchProgress.status === 'COMPLETED') {\r\n            this.getList();\r\n          }\r\n        }\r\n      });\r\n    },\r\n  },\r\n  // 添加页面激活时的处理\r\n  activated() {\r\n    // 从localStorage获取任务ID\r\n    const taskId = localStorage.getItem('currentSyncTaskId');\r\n    if (taskId) {\r\n      this.checkUnfinishedTask();\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    if (this.progressTimer) {\r\n      clearInterval(this.progressTimer);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.progress-content {\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n.progress-text {\r\n  margin-top: 10px;\r\n  color: #606266;\r\n}\r\n.error-message {\r\n  color: #F56C6C;\r\n  margin-top: 10px;\r\n  font-size: 14px;\r\n}\r\n.progress-info {\r\n  margin-top: 10px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n.time-range-info {\r\n  margin-top: 10px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n.el-descriptions {\r\n  margin: 20px;\r\n}\r\n.el-descriptions-item__content {\r\n  line-height: 24px;\r\n}\r\n.el-descriptions-item__content div {\r\n  margin: 8px 0;\r\n}\r\n.status-message {\r\n  margin-top: 5px;\r\n  font-size: 12px;\r\n  color: #606266;\r\n}\r\n.progress-text {\r\n  display: block;\r\n  font-size: 12px;\r\n  margin-top: 5px;\r\n}\r\n.status-message {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 5px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n.action-column {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.action-column .el-button {\r\n  margin-left: 0; /* 覆盖element-ui的默认margin */\r\n  width: 90px;    /* 固定宽度确保按钮统一 */\r\n}\r\n</style>\r\n"]}]}