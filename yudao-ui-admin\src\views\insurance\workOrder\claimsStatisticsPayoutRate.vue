<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px">
      <el-form-item label="创建时间">
        <el-date-picker v-model="createDateRange" style="width: 240px" value-format="yyyy-MM-dd"
                        type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="payout-rate-explanation">
      <el-alert
        title="赔付率计算说明"
        type="info"
        description="赔付率 = 已完成数 / (已完成数 + 待访问数) × 100%"
        show-icon
        :closable="false">
      </el-alert>
    </div>
    <el-table v-loading="loading" :data="claimsData">
      <el-table-column label="公司名称" prop="companyName" />
      <el-table-column label="已完成数" prop="finishedCount" />
      <el-table-column label="待访问数" prop="waitVisitingCount" />
      <el-table-column label="赔付率" :formatter="formatPayoutRate" />
    </el-table>
  </div>
</template>

<script>
import { getWorkOrderStat } from '@/api/insurance/workOrder';

export default {
  data() {
    return {
      claimsData: [],
      loading: true,
      createDateRange: [],
      queryParams: {}
    };
  },
  methods: {
    fetchData() {
      this.loading = true;
      let params = {...this.queryParams};
      this.addBeginAndEndTime(params, this.createDateRange, 'createTime');
      getWorkOrderStat(params).then(response => {
        this.claimsData = response.data;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    handleQuery() {
      this.fetchData();
    },
    resetQuery() {
      this.createDateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    formatPayoutRate(row) {
      const finished = row.finishedCount || 0;
      const waiting = row.waitVisitingCount || 0;
      const payoutRate = finished / (finished + waiting) * 100;
      return `${(payoutRate || 0).toFixed(2)}%`; // 格式化为百分比
    }
  },
  mounted() {
    this.fetchData();
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.payout-rate-explanation {
  margin-bottom: 20px;
}
</style> 