package cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
public class WorkOrderStatRespVo {
    private String companyName;
    private int waitTakingCount;
    private int waitHospitalCheckCount;
    private int waitProcessingCount;
    private int waitVisitingCount;
    private int finishedCount;
    private int rejectCount;
    private int delayCount;
    private BigDecimal money;
    private BigDecimal outpatientMoney;
    private BigDecimal inpatientMoney;
}
