
fda2d758d3634a15798d5e8865b914df2d72e390	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fchunk-vendors.js\",\"contentHash\":\"ee73d86c4e311e5000bda10e34ecfa36\"}","integrity":"sha512-RMq6WX4CzYml+dRbjycWQDd3ErNTdcImXM7Q2iSSDCHRForZagKaPSIhJUyAdx+omf0MVOmwCtLXwTOo5w1NTg==","time":1753927737846,"size":12241195}