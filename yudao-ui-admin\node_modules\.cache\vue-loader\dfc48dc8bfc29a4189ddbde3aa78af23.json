{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\claimsStatisticsPayoutRate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\claimsStatisticsPayoutRate.vue", "mtime": 1753840153009}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1667694382043}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRXb3JrT3JkZXJTdGF0IH0gZnJvbSAnQC9hcGkvaW5zdXJhbmNlL3dvcmtPcmRlcic7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgY2xhaW1zRGF0YTogW10sDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgY3JlYXRlRGF0ZVJhbmdlOiBbXSwNCiAgICAgIHF1ZXJ5UGFyYW1zOiB7fQ0KICAgIH07DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBmZXRjaERhdGEoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGV0IHBhcmFtcyA9IHsuLi50aGlzLnF1ZXJ5UGFyYW1zfTsNCiAgICAgIHRoaXMuYWRkQmVnaW5BbmRFbmRUaW1lKHBhcmFtcywgdGhpcy5jcmVhdGVEYXRlUmFuZ2UsICdjcmVhdGVUaW1lJyk7DQogICAgICBnZXRXb3JrT3JkZXJTdGF0KHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuY2xhaW1zRGF0YSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLmZldGNoRGF0YSgpOw0KICAgIH0sDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMuY3JlYXRlRGF0ZVJhbmdlID0gW107DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICBmb3JtYXRQYXlvdXRSYXRlKHJvdykgew0KICAgICAgY29uc3QgZmluaXNoZWQgPSByb3cuZmluaXNoZWRDb3VudCB8fCAwOw0KICAgICAgY29uc3Qgd2FpdGluZyA9IHJvdy53YWl0VmlzaXRpbmdDb3VudCB8fCAwOw0KICAgICAgY29uc3QgcGF5b3V0UmF0ZSA9IGZpbmlzaGVkIC8gKGZpbmlzaGVkICsgd2FpdGluZykgKiAxMDA7DQogICAgICByZXR1cm4gYCR7KHBheW91dFJhdGUgfHwgMCkudG9GaXhlZCgyKX0lYDsgLy8g5qC85byP5YyW5Li655m+5YiG5q+UDQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMuZmV0Y2hEYXRhKCk7DQogIH0NCn07DQo="}, {"version": 3, "sources": ["claimsStatisticsPayoutRate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "claimsStatisticsPayoutRate.vue", "sourceRoot": "src/views/insurance/workOrder", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"90px\">\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker v-model=\"createDateRange\" style=\"width: 240px\" value-format=\"yyyy-MM-dd\"\r\n                        type=\"daterange\" range-separator=\"-\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"payout-rate-explanation\">\r\n      <el-alert\r\n        title=\"赔付率计算说明\"\r\n        type=\"info\"\r\n        description=\"赔付率 = 已完成数 / (已完成数 + 待访问数) × 100%\"\r\n        show-icon\r\n        :closable=\"false\">\r\n      </el-alert>\r\n    </div>\r\n    <el-table v-loading=\"loading\" :data=\"claimsData\">\r\n      <el-table-column label=\"公司名称\" prop=\"companyName\" />\r\n      <el-table-column label=\"已完成数\" prop=\"finishedCount\" />\r\n      <el-table-column label=\"待访问数\" prop=\"waitVisitingCount\" />\r\n      <el-table-column label=\"赔付率\" :formatter=\"formatPayoutRate\" />\r\n    </el-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getWorkOrderStat } from '@/api/insurance/workOrder';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      claimsData: [],\r\n      loading: true,\r\n      createDateRange: [],\r\n      queryParams: {}\r\n    };\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      this.loading = true;\r\n      let params = {...this.queryParams};\r\n      this.addBeginAndEndTime(params, this.createDateRange, 'createTime');\r\n      getWorkOrderStat(params).then(response => {\r\n        this.claimsData = response.data;\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handleQuery() {\r\n      this.fetchData();\r\n    },\r\n    resetQuery() {\r\n      this.createDateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    formatPayoutRate(row) {\r\n      const finished = row.finishedCount || 0;\r\n      const waiting = row.waitVisitingCount || 0;\r\n      const payoutRate = finished / (finished + waiting) * 100;\r\n      return `${(payoutRate || 0).toFixed(2)}%`; // 格式化为百分比\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchData();\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.payout-rate-explanation {\r\n  margin-bottom: 20px;\r\n}\r\n</style> "]}]}