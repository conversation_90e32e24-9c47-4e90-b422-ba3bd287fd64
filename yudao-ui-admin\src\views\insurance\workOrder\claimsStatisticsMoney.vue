<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px">
      <el-form-item label="创建时间">
        <el-date-picker v-model="createDateRange" style="width: 240px" value-format="yyyy-MM-dd"
                        type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="claimsData">
      <el-table-column label="公司名称" prop="companyName" />
      <el-table-column label="门急诊金额" prop="outpatientMoney" />
      <el-table-column label="住院金额" prop="inpatientMoney" />
      <el-table-column label="金额" prop="money" />
    </el-table>
  </div>
</template>

<script>
import { getWorkOrderStat } from '@/api/insurance/workOrder';

export default {
  data() {
    return {
      claimsData: [],
      loading: true,
      createDateRange: [],
      queryParams: {}
    };
  },
  methods: {
    fetchData() {
      this.loading = true;
      let params = {...this.queryParams};
      this.addBeginAndEndTime(params, this.createDateRange, 'createTime');
      getWorkOrderStat(params).then(response => {
        this.claimsData = response.data;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    handleQuery() {
      this.fetchData();
    },
    resetQuery() {
      this.createDateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    }
  },
  mounted() {
    this.fetchData();
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style> 
