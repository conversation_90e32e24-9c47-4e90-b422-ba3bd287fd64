<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px">
      <el-form-item label="创建时间">
        <el-date-picker v-model="createDateRange" style="width: 240px" value-format="yyyy-MM-dd"
                        type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="acceptance-rate-explanation">
      <el-alert
        title="接单率计算说明"
        type="info"
        description="接单率 = (1 - 未接单数量 / 总单数) × 100%"
        show-icon
        :closable="false">
      </el-alert>
    </div>
    <el-table v-loading="loading" :data="orderData">
      <el-table-column label="公司名称" prop="companyName" />
      <el-table-column label="未接单数量" prop="waitTakingCount" />
      <el-table-column label="总单数" :formatter="formatTotalCount" />
      <el-table-column label="接单率" :formatter="formatAcceptanceRate" />
    </el-table>
  </div>
</template>

<script>
import { getWorkOrderStat } from '@/api/insurance/workOrder';

export default {
  name: "OrderAcceptanceRateStatistics",
  data() {
    return {
      orderData: [],
      loading: true,
      createDateRange: [],
      queryParams: {}
    };
  },
  methods: {
    fetchData() {
      this.loading = true;
      let params = {...this.queryParams};
      this.addBeginAndEndTime(params, this.createDateRange, 'createTime');
      getWorkOrderStat(params).then(response => {
        this.orderData = response.data;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    handleQuery() {
      this.fetchData();
    },
    resetQuery() {
      this.createDateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    formatAcceptanceRate(row) {
      // 未接单数量 (status = 0)
      const unaccepted = row.waitTakingCount || 0;
      // 总单数 (所有状态的工单总和)
      const total = (row.waitTakingCount || 0) + 
                    (row.waitProcessingCount || 0) + 
                    (row.waitVisitingCount || 0) + 
                    (row.finishedCount || 0) + 
                    (row.rejectCount || 0) + 
                    (row.delayCount || 0);
      
      // 避免除以零的情况
      if (total === 0) {
        return '0.00%';
      }
      
      // 接单率 = 100% - 未接单数量/总单数 * 100%
      const acceptanceRate = 100 - (unaccepted / total * 100);
      return `${acceptanceRate.toFixed(2)}%`;
    },
    formatTotalCount(row) {
      // 总单数 (所有状态的工单总和)
      const total = (row.waitTakingCount || 0) +
                    (row.waitProcessingCount || 0) +
                    (row.waitVisitingCount || 0) +
                    (row.finishedCount || 0) +
                    (row.rejectCount || 0) +
                    (row.delayCount || 0);
      return total;
    }
  },
  mounted() {
    this.fetchData();
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.acceptance-rate-explanation {
  margin-bottom: 20px;
}
</style>
