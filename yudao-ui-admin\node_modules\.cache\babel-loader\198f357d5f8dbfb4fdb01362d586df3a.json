{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\claimsStatisticsPayoutRate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\claimsStatisticsPayoutRate.vue", "mtime": 1753840153009}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\babel.config.js", "mtime": 1691889630125}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1667694382043}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;AA+BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAA,IADA,kBACA;IACA;MACAC,cADA;MAEAC,aAFA;MAGAC,mBAHA;MAIAC;IAJA;EAMA,CARA;EASAC;IACAC,SADA,uBACA;MAAA;;MACA;MACA;MACA;MACA;QACA;QACA;MACA,CAHA,EAGAC,KAHA,CAGA;QACA;MACA,CALA;IAMA,CAXA;IAYAC,WAZA,yBAYA;MACA;IACA,CAdA;IAeAC,UAfA,wBAeA;MACA;MACA;MACA;IACA,CAnBA;IAoBAC,gBApBA,4BAoBAC,GApBA,EAoBA;MACA;MACA;MACA;MACA,oDAJA,CAIA;IACA;EAzBA,CATA;EAoCAC,OApCA,qBAoCA;IACA;EACA;AAtCA,C", "names": ["data", "claimsData", "loading", "createDateRange", "queryParams", "methods", "fetchData", "catch", "handleQuery", "reset<PERSON><PERSON>y", "formatPayoutRate", "row", "mounted"], "sourceRoot": "src/views/insurance/workOrder", "sources": ["claimsStatisticsPayoutRate.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"90px\">\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker v-model=\"createDateRange\" style=\"width: 240px\" value-format=\"yyyy-MM-dd\"\r\n                        type=\"daterange\" range-separator=\"-\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"payout-rate-explanation\">\r\n      <el-alert\r\n        title=\"赔付率计算说明\"\r\n        type=\"info\"\r\n        description=\"赔付率 = 已完成数 / (已完成数 + 待访问数) × 100%\"\r\n        show-icon\r\n        :closable=\"false\">\r\n      </el-alert>\r\n    </div>\r\n    <el-table v-loading=\"loading\" :data=\"claimsData\">\r\n      <el-table-column label=\"公司名称\" prop=\"companyName\" />\r\n      <el-table-column label=\"已完成数\" prop=\"finishedCount\" />\r\n      <el-table-column label=\"待访问数\" prop=\"waitVisitingCount\" />\r\n      <el-table-column label=\"赔付率\" :formatter=\"formatPayoutRate\" />\r\n    </el-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getWorkOrderStat } from '@/api/insurance/workOrder';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      claimsData: [],\r\n      loading: true,\r\n      createDateRange: [],\r\n      queryParams: {}\r\n    };\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      this.loading = true;\r\n      let params = {...this.queryParams};\r\n      this.addBeginAndEndTime(params, this.createDateRange, 'createTime');\r\n      getWorkOrderStat(params).then(response => {\r\n        this.claimsData = response.data;\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handleQuery() {\r\n      this.fetchData();\r\n    },\r\n    resetQuery() {\r\n      this.createDateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    formatPayoutRate(row) {\r\n      const finished = row.finishedCount || 0;\r\n      const waiting = row.waitVisitingCount || 0;\r\n      const payoutRate = finished / (finished + waiting) * 100;\r\n      return `${(payoutRate || 0).toFixed(2)}%`; // 格式化为百分比\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchData();\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.payout-rate-explanation {\r\n  margin-bottom: 20px;\r\n}\r\n</style> "]}]}