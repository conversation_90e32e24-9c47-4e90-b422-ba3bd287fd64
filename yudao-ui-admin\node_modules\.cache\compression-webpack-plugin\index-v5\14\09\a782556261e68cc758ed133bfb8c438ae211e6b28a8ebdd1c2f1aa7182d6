
cab5e02032ceef7213dbe31e0e9d0593f112d4bc	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"b268e4a707f12314bb633f540a0dfed9\"}","integrity":"sha512-31NV6toUFlgeIHjpBpCg1tL9PLTdHjb6Nq//4Y+dQPWSjKjymlGezA6dwMcm0yS1lB6nZn1TqITrm42rVn+glw==","time":1753927737182,"size":3474877}