package cn.iocoder.yudao.module.insurance.service.wandaworkorder;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.insurance.controller.admin.compensating.*;
import cn.iocoder.yudao.module.insurance.controller.admin.wandaworkorder.vo.*;
import cn.iocoder.yudao.module.insurance.convert.wandaworkorder.WandaWorkOrderConvert;
import cn.iocoder.yudao.module.insurance.dal.dataobject.area.AreaCompanyDo;
import cn.iocoder.yudao.module.insurance.dal.dataobject.area.AreaDO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.diagnosis.DiagnosisDO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.disablebasiccard.DisableBasicCardDO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.irs.IrsPermanentResidentDo;
import cn.iocoder.yudao.module.insurance.dal.dataobject.permanentresident.PersonExtraInfoDo;
import cn.iocoder.yudao.module.insurance.dal.dataobject.wandaworkorder.WandaWorkOrderDO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrder2DO;
import cn.iocoder.yudao.module.insurance.dal.mysql.diagnosis.DiagnosisMapper;
import cn.iocoder.yudao.module.insurance.dal.mysql.disablebasiccard.DisableBasicCardMapper;
import cn.iocoder.yudao.module.insurance.dal.mysql.permanentresident.PersonExtraInfoMapper;
import cn.iocoder.yudao.module.insurance.dal.mysql.wandaworkorder.WandaWorkOrderMapper;
import cn.iocoder.yudao.module.insurance.enums.wanda.PersonInfoSourceEnum;
import cn.iocoder.yudao.module.insurance.enums.workorder.InsuranceTypeEnum;
import cn.iocoder.yudao.module.insurance.enums.workorder.WorkOrderTicketCompleteStatusEnum;
import cn.iocoder.yudao.module.insurance.service.area.AreaService;
import cn.iocoder.yudao.module.insurance.service.bankcard.BankCardService;
import cn.iocoder.yudao.module.insurance.service.compensating.CompensatingService;
import cn.iocoder.yudao.module.insurance.service.electronicbill.ElectronicBillService;
import cn.iocoder.yudao.module.insurance.service.insuranceauth.InsuranceAuthService;
import cn.iocoder.yudao.module.insurance.service.irs.IrsService;
import cn.iocoder.yudao.module.insurance.service.permanentresident.PermanentResidentService;
import cn.iocoder.yudao.module.insurance.service.wanda.WandaWorkOrderSyncService;
import cn.iocoder.yudao.module.insurance.service.workorder2.WorkOrder2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.insurance.enums.ErrorCodeConstants.WANDA_WORK_ORDER_ALREADY_GENERATED;
import static cn.iocoder.yudao.module.insurance.enums.ErrorCodeConstants.WANDA_WORK_ORDER_NOT_EXISTS;
import static cn.iocoder.yudao.module.insurance.enums.ErrorCodeConstants.WANDA_WORK_ORDER_DUPLICATE;

/**
 * 预处理工单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class WandaWorkOrderServiceImpl implements WandaWorkOrderService {

    @Resource
    private WandaWorkOrderMapper wandaWorkOrderMapper;

    @Resource
    private WandaWorkOrderSyncManager syncManager;

    @Resource
    private WandaWorkOrderSyncService wandaWorkOrderSyncService;
    
    @Resource
    private DiagnosisMapper icdDiagnosisMapper;
    
    @Resource
    private IrsService irsService;
    
    @Resource
    private AreaService areaService;
    
    @Resource
    private ElectronicBillService electronicBillService;
    
    @Resource
    private WorkOrder2Service workOrderService;
    
    @Resource
    private PermanentResidentService permanentResidentService;
    
    @Resource
    private BankCardService bankCardService;
    
    @Resource
    private PersonExtraInfoMapper personExtraInfoMapper;
    
    @Resource
    private DisableBasicCardMapper disableBasicCardMapper;
    
    @Autowired
    private ApplicationContext applicationContext;
    
    @Autowired
    private CompensatingService compensatingService;

    @Resource(name="transactionManager")
    private DataSourceTransactionManager transactionManager;

    @Resource
    private WandaWorkOrderBatchManager batchManager;

    @Resource
    private InsuranceAuthService insuranceAuthService;

    @Override
    public Long createWandaWorkOrder(WandaWorkOrderCreateReqVO createReqVO) {
        // 插入
        WandaWorkOrderDO wandaWorkOrder = WandaWorkOrderConvert.INSTANCE.convert(createReqVO);
        wandaWorkOrderMapper.insert(wandaWorkOrder);
        // 返回
        return wandaWorkOrder.getId();
    }

    @Override
    public void updateWandaWorkOrder(WandaWorkOrderUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateWandaWorkOrderExists(updateReqVO.getId());
        // 更新
        WandaWorkOrderDO updateObj = WandaWorkOrderConvert.INSTANCE.convert(updateReqVO);
        wandaWorkOrderMapper.updateById(updateObj);
    }

    @Override
    public void deleteWandaWorkOrder(Long id) {
        // 校验存在
        this.validateWandaWorkOrderExists(id);
        // 删除
        wandaWorkOrderMapper.deleteById(id);
    }

    private void validateWandaWorkOrderExists(Long id) {
        if (wandaWorkOrderMapper.selectById(id) == null) {
            throw exception(WANDA_WORK_ORDER_NOT_EXISTS);
        }
    }

    @Override
    public WandaWorkOrderDO getWandaWorkOrder(Long id) {
        return wandaWorkOrderMapper.selectById(id);
    }

    @Override
    public List<WandaWorkOrderDO> getWandaWorkOrderList(Collection<Long> ids) {
        return wandaWorkOrderMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<WandaWorkOrderDO> getWandaWorkOrderPage(WandaWorkOrderPageReqVO pageReqVO) {
        return wandaWorkOrderMapper.selectPage(pageReqVO);
    }

    @Override
    public List<WandaWorkOrderDO> getWandaWorkOrderList(WandaWorkOrderExportReqVO exportReqVO) {
        return wandaWorkOrderMapper.selectList(exportReqVO);
    }

    @Override
    public String syncWandaWorkOrder(String startTime, String endTime) {
        // 创建同步任务
        String taskId = syncManager.createTask(startTime, endTime);
        
        // 锁定任务，防止前端显示为可继续同步
        if (!syncManager.lockTask(taskId)) {
            log.warn("[syncWandaWorkOrder][任务({})] 无法锁定新创建的任务", taskId);
        }
        
        // 异步执行同步
        startSync(taskId, startTime, endTime, 1);
        
        return taskId;
    }

    @Override
    public void resumeSync(String taskId) {
        // 先检查任务是否被锁定
        if (syncManager.isTaskLocked(taskId)) {
            throw new ServiceException(500, "该任务正在同步中，请稍后再试");
        }
        // 尝试锁定任务
        if (!syncManager.lockTask(taskId)) {
            throw new ServiceException(500, "无法锁定任务，请稍后再试");
        }
        try {
            WandaWorkOrderSyncManager.SyncTask task = syncManager.resumeTask(taskId);
            if (task == null) {
                throw new ServiceException(500, "同步任务不存在或状态错误");
            }
            // 从中断的页码继续同步
            startSync(taskId, task.getStartTime(), task.getEndTime(), Math.max(task.getCurrentPage(), 1));
        } catch (Exception e) {
            // 如果发生异常，解锁任务
            syncManager.unlockTask(taskId);
            throw e;
        }
    }

    private void startSync(String taskId, String startTime, String endTime, int startPage) {
        CompletableFuture.runAsync(() -> {
            try {
                wandaWorkOrderSyncService.syncWorkOrders(startTime, endTime, taskId, startPage);
                // 同步完成
                syncManager.updateTask(taskId, 100, "COMPLETED", "同步完成");
            } catch (Exception e) {
                log.error("[syncWandaWorkOrder][任务({}) 同步异常]", taskId, e);
                syncManager.updateTaskError(taskId, e.getMessage());
            } finally {
                // 无论成功失败，最后都解锁任务
                syncManager.unlockTask(taskId);
            }
        });
    }

    @Override
    public WandaWorkOrderSyncProgressVO getSyncProgress(String taskId) {
        WandaWorkOrderSyncProgressVO progress = syncManager.getProgress(taskId);
        if (progress == null) {
            throw new ServiceException(500, "同步任务不存在");
        }
        return progress;
    }

    @Override
    public WandaWorkOrderSyncProgressVO getUnfinishedTask() {
        List<WandaWorkOrderSyncProgressVO>  unfinishedTaskList = syncManager.getUnfinishedTaskList();
        return unfinishedTaskList.isEmpty()? null: unfinishedTaskList.get(0);
    }

    @Override
    public List<WandaWorkOrderSyncProgressVO> getUnfinishedTaskList() {
        return syncManager.getUnfinishedTaskList();
    }

    @Override
    public WandaWorkOrderStatsRespVO getWandaWorkOrderStats() {
        return wandaWorkOrderMapper.selectFieldStats();
    }

    @Override
    public void generateWorkOrder(Long id) {
        WandaWorkOrderDO workOrder = null;
        StringBuilder failReason = new StringBuilder();
        boolean success = false;
        String errorMsg = null;
        
        try {
            // 1. 获取工单
            workOrder = wandaWorkOrderMapper.selectById(id);
            if (workOrder == null) {
                throw exception(WANDA_WORK_ORDER_NOT_EXISTS);
            }
            
            // 2. 验证状态
            if (workOrder.getStatus() == 1) { // 已生成工单
                throw exception(WANDA_WORK_ORDER_ALREADY_GENERATED);
            }
            
            // 3. 检查是否存在相同工单
            if (existsSameWorkOrder(workOrder.getIdCardNumber(), workOrder.getHospitalCode(), 
                    workOrder.getTreatmentSerialNumber(), workOrder.getTreatmentSerialNumberType())) {
                throw exception(WANDA_WORK_ORDER_DUPLICATE);
            }
            
            // 4. 生成工单
            if (workOrder.getTreatmentSerialNumberType() == 1) { // 住院
                if (workOrder.getOrderType() == 1) { // 老年人意外险
                    try {
                        success = generateOldPeopleAccidentWorkOrder(workOrder, failReason);
                        if (!success && failReason.length() == 0) {
                            failReason.append("不满足老年人意外险住院工单生成条件");
                        }
                    } catch (Exception e) {
                        failReason.append("生成老年人意外险住院工单异常：").append(e.getMessage());
                        errorMsg = e.getMessage();
                    }
                } else if (workOrder.getOrderType() == 2) { // 残疾人
                    try {
                        success = generateDisabledPersonWorkOrder(workOrder, failReason);
                        if (!success && failReason.length() == 0) {
                            failReason.append("不满足残疾人住院工单生成条件");
                        }
                    } catch (Exception e) {
                        failReason.append("生成残疾人住院工单异常：").append(e.getMessage());
                        errorMsg = e.getMessage();
                    }
                } else {
                    failReason.append("未知的工单类型:").append(workOrder.getOrderType());
                }
            } else if (workOrder.getTreatmentSerialNumberType() == 0) { // 门诊
                if (workOrder.getOrderType() == 1) { // 老年人意外险
                    try {
                        success = generateOutpatientWorkOrder(workOrder, failReason);
                        if (!success && failReason.length() == 0) {
                            failReason.append("不满足老年人意外险门诊工单生成条件");
                        }
                    } catch (Exception e) {
                        failReason.append("生成老年人意外险门诊工单异常：").append(e.getMessage());
                        errorMsg = e.getMessage();
                    }
                } else {
                    failReason.append("门诊不支持残疾人险");
                }
            } else {
                failReason.append("未知的就诊类型:").append(workOrder.getTreatmentSerialNumberType());
            }
        } catch (Exception e) {
            // 记录错误
            errorMsg = e.getMessage();
            failReason.append("处理异常：").append(errorMsg);
            log.error("生成工单异常, id: {}, 异常信息: {}", id, errorMsg, e);
        } finally {
            // 无论如何都要更新状态，使用新事务确保不会回滚
            updateWorkOrderStatusWithNewTransaction(id, success, failReason.toString());
            
            // 如果处理失败且有错误信息，则抛出异常
            if (!success && errorMsg != null) {
                throw new ServiceException(500, errorMsg);
            } else if (!success) {
                throw new ServiceException(500, failReason.toString());
            }
        }
    }

    /**
     * 获取诊断编码列表
     */
    private List<String> getZdbmList(WandaWorkOrderDO workOrder) {
        List<String> zdbmList = new ArrayList<>();
        
        // 添加主诊断编码 - 使用|分隔符
        if (StrUtil.isNotBlank(workOrder.getMainDiagnosisCode())) {
            Arrays.stream(workOrder.getMainDiagnosisCode().split("\\|"))
                    .filter(StrUtil::isNotBlank)
                    .map(String::toLowerCase)
                    .forEach(zdbmList::add);
        }
        
        // 添加其他诊断编码 - 使用|分隔符
        if (StrUtil.isNotBlank(workOrder.getOtherDiagnosisCode())) {
            Arrays.stream(workOrder.getOtherDiagnosisCode().split("\\|"))
                  .filter(StrUtil::isNotBlank)
                  .map(String::toLowerCase)
                  .forEach(zdbmList::add);
        }
        
        return zdbmList;
    }

    /**
     * 生成老年人意外险住院工单
     * @return 成功返回true，失败返回false并通过out参数提供失败原因
     */
    private boolean generateOldPeopleAccidentWorkOrder(WandaWorkOrderDO workOrder, StringBuilder failReasonOut) {
        String idcard = workOrder.getIdCardNumber();
        
        // 检查授权
        if (!checkAuthorization(idcard)) {
            failReasonOut.append("患者未授权");
            log.info("住院记录({}-{})对应患者未授权", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }
        
        // 检查年龄
        if (IdcardUtil.getAgeByIdCard(idcard) < 60) {
            failReasonOut.append("患者年龄小于60岁，不符合老年人意外险条件");
            log.info("住院记录({}-{})对应患者年龄小于60岁", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }

        // 检查诊断编码
        List<String> zdbmList = getZdbmList(workOrder);
        if (zdbmList.isEmpty()) {
            failReasonOut.append("缺少诊断编码");
            log.info("住院记录({}-{})无诊断编码", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }

        // 检查是否意外险诊断
        List<DiagnosisDO> diagnosisDOS = icdDiagnosisMapper.selectListByCollection(zdbmList);
        if (diagnosisDOS == null || diagnosisDOS.isEmpty()) {
            failReasonOut.append("诊断编码不在意外险ICD表内");
            log.info("住院记录({}-{})诊断明细不在意外icd表内", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }

        // 检查户籍
        IrsPermanentResidentDo resident = irsService.getPermanentResident(idcard);
        if (resident == null) {
            failReasonOut.append("住院记录对应患者非本地户口");
            log.info("住院记录({}-{})对应患者非本地户口", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }

        // 获取区域
        AreaDO area = getArea(resident);
        if (area == null) {
            failReasonOut.append("住院记录对应患者找不到区域");
            log.info("住院记录({}-{})对应患者找不到区域", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }

        // 获取保险公司
        AreaCompanyDo areaCompany = areaService.getResponsibleCompany(area.getId(), workOrder.getTreatmentDatetime(), 
            InsuranceTypeEnum.HUZHOU_OLD_PEOPLE_ACCIDENT_INSURANCE);
        if (areaCompany == null) {
            failReasonOut.append("住院记录对应患者所在区域无负责保险公司");
            log.info("住院记录({}-{})对应患者所在区域无负责保险公司", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }

        // 创建工单并处理相关数据
        try {
            // 保存来源信息
            saveResidentSourceInfo(resident);
            saveProvincePoliceSourceInfo(resident);
            saveWandaSourceInfo(workOrder, idcard);
            
            // 创建工单
            WorkOrder2DO workOrder2 = convertToWorkOrder2(workOrder, resident, area, areaCompany);
            workOrder2.setType(InsuranceTypeEnum.HUZHOU_OLD_PEOPLE_ACCIDENT_INSURANCE.getCode());
            
            // 处理电子票据
            electronicBillService.workOrderHandler(workOrder2);
            
            // 计算赔付金额
            InsuranceTypeEnum insuranceTypeEnum = InsuranceTypeEnum.getByCode(workOrder2.getType());
            BasicEstimateHandler estimateHandler = applicationContext.getBean(insuranceTypeEnum.getEstimateHandlerClazz());
            EstimateResult estimateResult = estimateHandler.apply(new EstimateParam(workOrder2, CompensatingOption.DEFAULT_COMPENSATING_OPTION.updateInsuranceBeginDate(areaCompany.getAgreementDate())));
            SuggestCompensatingMoney suggestCompensatingMoney = estimateResult.getSuggestCompensatingMoney();
            workOrder2.setMedicalCosts(suggestCompensatingMoney.getMedicalCosts());
            workOrder2.setHospitalizationAllowance(suggestCompensatingMoney.getHospitalizationAllowance());
            workOrder2.setDiagnosisAllowance(suggestCompensatingMoney.getDiagnosisAllowance());
            workOrder2.setSuggestCompensatingMoney(suggestCompensatingMoney.getSum());
            
            // 保存数据并生成PDF
            permanentResidentService.createByIrsPermanentResidentDO(resident);
            workOrderService.createWorkOrderDo(workOrder2);
            bankCardService.createBankCardBySocialMedicareCardBankAccount(idcard);
            workOrderService.buildUnsignedPdf(workOrder2);
            workOrderService.buildInsuancePolicy(workOrder2.getIdCardNumber(), workOrder2.getMobilePhoneNumber(), workOrder2.getAddress());
            
            return true;
        } catch (Exception e) {
            log.error("生成老年人意外险住院工单异常", e);
            throw e;
        }
    }
    
    /**
     * 生成残疾人险住院工单
     * @return 成功返回true，失败返回false并通过out参数提供失败原因
     */
    private boolean generateDisabledPersonWorkOrder(WandaWorkOrderDO workOrder, StringBuilder failReasonOut) {
        String idcard = workOrder.getIdCardNumber();
        
        // 检查授权
        if (!checkAuthorization(idcard)) {
            failReasonOut.append("患者未授权");
            log.info("住院记录({}-{})对应患者未授权", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }
        
        // 检查是否是残疾人
        DisableBasicCardDO disabledPersonData = disableBasicCardMapper.selectByIdcard(idcard);
        if (disabledPersonData == null) {
            failReasonOut.append("患者不是残疾人");
            log.info("住院记录({}-{})对应患者不是残疾人", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }

        // 检查户籍
        IrsPermanentResidentDo resident = irsService.getPermanentResident(idcard);
        if (resident == null) {
            failReasonOut.append("住院记录对应患者非本地户口");
            log.info("住院记录({}-{})对应患者非本地户口", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }

        // 判定保险类型和获取保险公司
        List<String> zdbmList = getZdbmList(workOrder);
        InsuranceTypeEnum typeEnum = InsuranceTypeEnum.HUZHOU_DISABLED_PEOPLE_DISEASE_INSURANCE; // 默认疾病住院险
        
        // 判断是否意外险
        List<DiagnosisDO> diagnosisDetailDos = icdDiagnosisMapper.selectListByCollection(zdbmList);
        if (diagnosisDetailDos != null && !diagnosisDetailDos.isEmpty()) {
            typeEnum = InsuranceTypeEnum.HUZHOU_DISABLED_PEOPLE_ACCIDENT_INSURANCE;
        }

        AreaDO area = getArea(resident);
        if (area == null) {
            failReasonOut.append("住院记录对应患者找不到区域");
            log.info("住院记录({}-{})对应患者找不到区域", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }
        
        // 获取保险公司
        AreaCompanyDo areaCompany = areaService.getResponsibleCompany(area.getId(), workOrder.getTreatmentDatetime(), typeEnum);
        if (areaCompany == null) {
            failReasonOut.append("住院记录对应患者所在区域无负责保险公司");
            log.info("住院记录({}-{})对应患者所在区域无负责保险公司", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }
        
        try {
            // 保存来源信息
            saveResidentSourceInfo(resident);
            saveProvincePoliceSourceInfo(resident);
            saveWandaSourceInfo(workOrder, idcard);
            
            // 创建工单
            WorkOrder2DO workOrder2 = convertToWorkOrder2(workOrder, resident, area, areaCompany);
            workOrder2.setType(typeEnum.getCode());
            
            // 处理电子票据
            electronicBillService.workOrderHandler(workOrder2);
            
            // 计算赔付金额
            InsuranceTypeEnum insuranceTypeEnum = InsuranceTypeEnum.getByCode(workOrder2.getType());
            BasicEstimateHandler estimateHandler = applicationContext.getBean(insuranceTypeEnum.getEstimateHandlerClazz());
            EstimateResult estimateResult = estimateHandler.apply(new EstimateParam(workOrder2, CompensatingOption.DEFAULT_COMPENSATING_OPTION.updateInsuranceBeginDate(areaCompany.getAgreementDate())));
            SuggestCompensatingMoney suggestCompensatingMoney = estimateResult.getSuggestCompensatingMoney();
            workOrder2.setMedicalCosts(suggestCompensatingMoney.getMedicalCosts());
            workOrder2.setHospitalizationAllowance(suggestCompensatingMoney.getHospitalizationAllowance());
            workOrder2.setDiagnosisAllowance(suggestCompensatingMoney.getDiagnosisAllowance());
            workOrder2.setSuggestCompensatingMoney(suggestCompensatingMoney.getSum());
            
            // 保存数据并生成PDF
            permanentResidentService.createByIrsPermanentResidentDO(resident);
            workOrderService.createWorkOrderDo(workOrder2);
            bankCardService.createBankCardBySocialMedicareCardBankAccount(idcard);
            workOrderService.buildUnsignedPdf(workOrder2);
            workOrderService.buildInsuancePolicy(workOrder2.getIdCardNumber(), workOrder2.getMobilePhoneNumber(), workOrder2.getAddress());
            
            return true;
        } catch (Exception e) {
            log.error("生成残疾人险住院工单异常", e);
            throw e;
        }
    }
    
    /**
     * 生成门诊工单
     * @return 成功返回true，失败返回false并通过out参数提供失败原因
     */
    private boolean generateOutpatientWorkOrder(WandaWorkOrderDO workOrder, StringBuilder failReasonOut) {
        // 基本检查
        String idcard = workOrder.getIdCardNumber();
        
        // 检查授权
        if (!checkAuthorization(idcard)) {
            failReasonOut.append("患者未授权");
            log.info("门诊记录({}-{})对应患者未授权", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }

        // 检查年龄
        if (IdcardUtil.getAgeByIdCard(idcard) < 60) {
            failReasonOut.append("患者年龄小于60岁，不符合老年人意外险条件");
            log.info("门诊记录({}-{})对应患者年龄小于60岁", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }

        // 检查诊断编码
        List<String> zdbmList = getZdbmList(workOrder);
        if (zdbmList.isEmpty()) {
            failReasonOut.append("缺少诊断编码");
            log.info("门诊记录({}-{})无诊断编码", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }
        
        // 检查是否意外险诊断
        List<DiagnosisDO> diagnosisDOS = icdDiagnosisMapper.selectListByCollection(zdbmList);
        if (diagnosisDOS == null || diagnosisDOS.isEmpty()) {
            failReasonOut.append("诊断编码不在意外险ICD表内");
            log.info("门诊记录({}-{})诊断明细不在意外icd表内", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }
        
        // 检查户籍
        IrsPermanentResidentDo resident = irsService.getPermanentResident(idcard);
        if (resident == null) {
            failReasonOut.append("门诊记录对应患者非本地户口");
            log.info("门诊记录({}-{})对应患者非本地户口", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }
        
        // 获取区域
        AreaDO area = getArea(resident);
        if (area == null) {
            failReasonOut.append("门诊记录对应患者找不到区域");
            log.info("门诊记录({}-{})对应患者找不到区域", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }
        
        // 获取保险公司
        AreaCompanyDo areaCompany = areaService.getResponsibleCompany(area.getId(), workOrder.getTreatmentDatetime(), 
            InsuranceTypeEnum.HUZHOU_OLD_PEOPLE_ACCIDENT_INSURANCE);
        if (areaCompany == null) {
            failReasonOut.append("门诊记录对应患者所在区域无负责保险公司");
            log.info("门诊记录({}-{})对应患者所在区域无负责保险公司", workOrder.getHospitalCode(), workOrder.getTreatmentSerialNumber());
            return false;
        }
        
        try {
            // 保存来源信息
            saveResidentSourceInfo(resident);
            saveProvincePoliceSourceInfo(resident);
            saveWandaSourceInfo(workOrder, idcard);
            
            // 创建工单
            WorkOrder2DO workOrder2 = convertToWorkOrder2(workOrder, resident, area, areaCompany);
            workOrder2.setType(InsuranceTypeEnum.HUZHOU_OLD_PEOPLE_ACCIDENT_INSURANCE.getCode());
            
            // 处理电子票据
            electronicBillService.workOrderHandler(workOrder2);
            
            // 计算赔付金额
            InsuranceTypeEnum insuranceTypeEnum = InsuranceTypeEnum.getByCode(workOrder2.getType());
            BasicEstimateHandler estimateHandler = applicationContext.getBean(insuranceTypeEnum.getEstimateHandlerClazz());
            EstimateResult estimateResult = estimateHandler.apply(new EstimateParam(workOrder2, CompensatingOption.DEFAULT_COMPENSATING_OPTION.updateInsuranceBeginDate(areaCompany.getAgreementDate())));
            SuggestCompensatingMoney suggestCompensatingMoney = estimateResult.getSuggestCompensatingMoney();
            workOrder2.setMedicalCosts(suggestCompensatingMoney.getMedicalCosts());
            workOrder2.setHospitalizationAllowance(suggestCompensatingMoney.getHospitalizationAllowance());
            workOrder2.setDiagnosisAllowance(suggestCompensatingMoney.getDiagnosisAllowance());
            workOrder2.setSuggestCompensatingMoney(suggestCompensatingMoney.getSum());
            
            // 保存数据并生成PDF
            permanentResidentService.createByIrsPermanentResidentDO(resident);
            workOrderService.createWorkOrderDo(workOrder2);
            bankCardService.createBankCardBySocialMedicareCardBankAccount(idcard);
            workOrderService.buildUnsignedPdf(workOrder2);
            workOrderService.buildInsuancePolicy(workOrder2.getIdCardNumber(), workOrder2.getMobilePhoneNumber(), workOrder2.getAddress());
            
            return true;
        } catch (Exception e) {
            log.error("生成门诊工单异常", e);
            throw e;
        }
    }
    
    /**
     * 获取区域信息
     */
    private AreaDO getArea(IrsPermanentResidentDo resident) {
        if (StrUtil.isBlank(resident.getBas_census_name()) || StrUtil.isBlank(resident.getBas_township_sname())) {
            return areaService.getAreaByCensusAddr(resident.getBas_census_addr());
        } else {
            return areaService.getAreaByCensusAndStreet(resident.getBas_census_name(), resident.getBas_township_sname());
        }
    }
    
    /**
     * 将预处理工单转换为正式工单
     */
    private WorkOrder2DO convertToWorkOrder2(WandaWorkOrderDO workOrder, IrsPermanentResidentDo resident, AreaDO area, AreaCompanyDo areaCompany) {
        WorkOrder2DO workOrder2 = new WorkOrder2DO();
        
        // 医院信息
        workOrder2.setHospitalCode(workOrder.getHospitalCode());
        workOrder2.setHospitalName(workOrder.getHospitalName());
        workOrder2.setHospitalLevel(workOrder.getHospitalLevel());
        
        // 就诊信息
        workOrder2.setTreatmentSerialNumberType(workOrder.getTreatmentSerialNumberType());
        workOrder2.setTreatmentSerialNumber(workOrder.getTreatmentSerialNumber());
        workOrder2.setTreatmentDatetime(workOrder.getTreatmentDatetime());
        workOrder2.setTreatmentType(workOrder.getTreatmentType());
        
        // 患者信息
        workOrder2.setName(workOrder.getName());
        workOrder2.setGender(IdcardUtil.getGenderByIdCard(workOrder.getIdCardNumber()) == 1 ? "男" : "女");
        workOrder2.setIdCardNumber(workOrder.getIdCardNumber());
        workOrder2.setAddress(StrUtil.isNotBlank(workOrder.getAddress()) ? workOrder.getAddress() : resident.getBas_census_addr());
        workOrder2.setMobilePhoneNumber(StrUtil.isNotBlank(workOrder.getMobilePhoneNumber()) ? workOrder.getMobilePhoneNumber() : resident.getBas_phone());
        workOrder2.setContactPersonName(workOrder.getContactPersonName());
        workOrder2.setContactPersonPhoneNumber(workOrder.getContactPersonPhoneNumber());
        
        // 科室医生信息
        workOrder2.setDepartmentCode(workOrder.getDepartmentCode());
        workOrder2.setDepartmentName(workOrder.getDepartmentName());
        workOrder2.setDoctorCode(workOrder.getDoctorCode());
        workOrder2.setDoctorName(workOrder.getDoctorName());
        
        // 住院信息
        workOrder2.setHospitalizationNumber(workOrder.getHospitalizationNumber());
        workOrder2.setInpatientArea(workOrder.getInpatientArea());
        workOrder2.setBedNumber(workOrder.getBedNumber());
        workOrder2.setInHospitalDatetime(workOrder.getInHospitalDatetime());
        workOrder2.setLeaveHospitalDatetime(workOrder.getLeaveHospitalDatetime());
        workOrder2.setLeaveHospitalState(workOrder.getLeaveHospitalState());
        workOrder2.setLeaveHospitalDiagnosisName(workOrder.getLeaveHospitalDiagnosisName());
        workOrder2.setInHospitalDiagnosisName(workOrder.getInHospitalDiagnosisName());
        workOrder2.setLeaveHospitalDescription(workOrder.getLeaveHospitalDescription());
        
        // 诊断信息
        workOrder2.setMainDiagnosisCode(workOrder.getMainDiagnosisCode());
        workOrder2.setMainDiagnosisName(workOrder.getMainDiagnosisName());
        workOrder2.setOtherDiagnosisCode(workOrder.getOtherDiagnosisCode());
        workOrder2.setOtherDiagnosisName(workOrder.getOtherDiagnosisName());
        workOrder2.setComplicationCode(workOrder.getComplicationCode());
        
        // 病史信息
        workOrder2.setMainComplaint(workOrder.getMainComplaint());
        workOrder2.setCurrentMedicalHistory(workOrder.getCurrentMedicalHistory());
        workOrder2.setPastMedicalHistory(workOrder.getPastMedicalHistory());
        workOrder2.setGeneticHistory(workOrder.getGeneticHistory());
        
        // 票据信息
        workOrder2.setElectronicBillIds(workOrder.getElectronicBillIds());
        
        // 区域信息
        workOrder2.setAreaId(area.getId());
        
        // 状态
        workOrder2.setStatus(0);
        
        return workOrder2;
    }
    
    /**
     * 保存人口库来源信息
     */
    private void saveResidentSourceInfo(IrsPermanentResidentDo resident) {
        try {
            PersonExtraInfoDo personExtraInfoDo = new PersonExtraInfoDo();
            personExtraInfoDo.setSource(PersonInfoSourceEnum.PERSON_DATABASE.getType());
            personExtraInfoDo.setIdcard(resident.getBas_id_card());
            personExtraInfoDo.setPhone(resident.getBas_phone());
            personExtraInfoDo.setAddress(resident.getBas_census_addr());
            personExtraInfoMapper.insert(personExtraInfoDo);
        } catch (DuplicateKeyException e) {
            log.info("人口库来源信息重复");
        } catch (Exception e) {
            log.warn("人口库来源信息插入异常", e);
        }
    }
    
    /**
     * 保存省公安厅身份证来源信息
     */
    private void saveProvincePoliceSourceInfo(IrsPermanentResidentDo resident) {
        try {
            // 获取身份证信息
            try {
                // 如果需要保存省公安厅信息，可以参考ZyWorkOrderServiceImpl中的实现
                // 这里简化处理
            } catch (ServiceException e) {
                log.warn(e.getMessage(), e);
            }
        } catch (Exception e) {
            log.warn("省公安厅身份证来源信息插入异常", e);
        }
    }
    
    /**
     * 保存万达数据来源信息
     */
    private void saveWandaSourceInfo(WandaWorkOrderDO workOrder, String idcard) {
        try {
            if (StrUtil.isNotBlank(workOrder.getAddress())) {
                PersonExtraInfoDo personExtraInfoDo = new PersonExtraInfoDo();
                personExtraInfoDo.setSource(PersonInfoSourceEnum.WANDA.getType());
                personExtraInfoDo.setIdcard(idcard);
                personExtraInfoDo.setPhone(workOrder.getMobilePhoneNumber());
                personExtraInfoDo.setAddress(workOrder.getAddress());
                personExtraInfoMapper.insert(personExtraInfoDo);
            }
        } catch (DuplicateKeyException e) {
            log.warn("万达医疗来源信息插入异常", e);
        } catch (Exception e) {
            log.warn("万达医疗来源信息插入异常", e);
        }
    }

    @Override
    public boolean isTaskLocked(String taskId) {
        return syncManager.isTaskLocked(taskId);
    }

    @Override
    public void downloadBatchGenerateReport(String taskId, HttpServletResponse response) throws IOException {
        // 获取任务结果
        List<WandaWorkOrderBatchManager.ProcessResult> results = batchManager.getResults(taskId);
        if (results.isEmpty()) {
            throw new ServiceException(500, "没有可导出的结果");
        }
        
        // 创建Excel VO类列表
        List<WandaWorkOrderBatchReportVO> reportVOList = results.stream().map(result -> {
            WandaWorkOrderBatchReportVO vo = new WandaWorkOrderBatchReportVO();
            vo.setId(String.valueOf(result.getId()));
            vo.setHospitalCode(result.getHospitalCode());
            vo.setHospitalName(result.getHospitalName());
            vo.setTreatmentSerialNumber(result.getTreatmentSerialNumber());
            vo.setName(result.getName());
            vo.setIdCardNumber(result.getIdCardNumber());
            vo.setOrderType(result.getOrderType() == 1 ? "老年人意外险" : (result.getOrderType() == 2 ? "残疾人险" : String.valueOf(result.getOrderType())));
            vo.setStatus(result.isSuccess() ? "成功" : "失败");
            vo.setFailReason(result.getFailReason());
            vo.setProcessTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(result.getProcessTime()));
            return vo;
        }).collect(Collectors.toList());
        
        // 生成Excel - 使用兼容的方法
        ExcelUtils.write(response, "批量生成工单报告.xlsx", "生成结果", WandaWorkOrderBatchReportVO.class, reportVOList);
    }

    @Override
    public String batchGenerateWorkOrder(WandaWorkOrderPageReqVO queryParams) {
        // 检查是否有未完成的任务
        List<WandaWorkOrderBatchProgressVO> unfinishedTasks = batchManager.getUnfinishedTasks();
        if (!unfinishedTasks.isEmpty()) {
            throw new ServiceException(500, "存在未完成的批量生成任务,请等待当前任务完成后再试");
        }
        
        // 创建批处理任务
        String taskId = batchManager.createTask();
        
        // 异步执行批量处理
        CompletableFuture.runAsync(() -> {
            try {
                processBatchGenerate(taskId, queryParams);
            } catch (Exception e) {
                log.error("[batchGenerateWorkOrder][任务({})] 批量处理异常", taskId, e);
                batchManager.failTask(taskId, e.getMessage());
            }
        });
        
        return taskId;
    }

    /**
     * 处理批量生成工单
     *
     * @param taskId 任务ID
     * @param reqVo 查询参数
     */
    private void processBatchGenerate(String taskId, WandaWorkOrderPageReqVO reqVo) {
        // 只处理未处理的工单
        reqVo.setStatus(0); // 未处理

        // 1. 首先获取所有需要处理的工单ID，避免在处理过程中因状态变更导致分页数据不一致
        List<Long> recordIds = new ArrayList<>();
        int pageNo = 1;
        int pageSize = 200; // 分页查询ID时，每页数量
        reqVo.setPageSize(pageSize);

        // 使用循环获取所有页的ID
        while (true) {
            reqVo.setPageNo(pageNo++);
            PageResult<WandaWorkOrderDO> pageResult = wandaWorkOrderMapper.selectPage(reqVo);
            if (pageResult.getList().isEmpty()) {
                break;
            }
            for (WandaWorkOrderDO item : pageResult.getList()) {
                recordIds.add(item.getId());
            }
            // 如果返回的列表大小小于页面大小，说明是最后一页
            if (pageResult.getList().size() < pageSize) {
                break;
            }
        }

        int totalCount = recordIds.size();
        if (totalCount == 0) {
            batchManager.updateTotalCount(taskId, 0);
            batchManager.completeTask(taskId);
            return;
        }

        // 更新总数
        batchManager.updateTotalCount(taskId, totalCount);

        // 使用线程池并行处理
        int processors = Runtime.getRuntime().availableProcessors();
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                processors, // 核心线程数
                processors * 2, // 最大线程数
                60, TimeUnit.SECONDS, // 线程空闲时间
                new LinkedBlockingQueue<>(totalCount), // 工作队列
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );

        try {
            // 2. 遍历ID列表，提交处理任务
            for (Long recordId : recordIds) {
                executor.execute(() -> {
                    WandaWorkOrderDO record = wandaWorkOrderMapper.selectById(recordId);
                    // 增加一次检查，确保记录存在且状态仍为未处理
                    if (record != null && record.getStatus() == 0) {
                        processRecord(taskId, record);
                    }
                });
            }

            // 3. 等待所有任务完成
            executor.shutdown();
            if (!executor.awaitTermination(1, TimeUnit.HOURS)) {
                log.warn("[processBatchGenerate][任务({})] 等待任务完成超时", taskId);
                executor.shutdownNow();
            }

            // 4. 任务全部完成
            batchManager.completeTask(taskId);
        } catch (Exception e) {
            log.error("[processBatchGenerate][任务({})] 处理异常", taskId, e);
            batchManager.failTask(taskId, e.getMessage());
            // 尝试关闭线程池
            executor.shutdownNow();
        }
    }

    @Override
    public String batchRegenerateFailedWorkOrder(WandaWorkOrderPageReqVO queryParams) {
        // 检查是否有未完成的任务
        List<WandaWorkOrderBatchProgressVO> unfinishedTasks = batchManager.getUnfinishedTasks();
        if (!unfinishedTasks.isEmpty()) {
            throw new ServiceException(500, "存在未完成的批量生成任务,请等待当前任务完成后再试");
        }
        
        // 创建批处理任务
        String taskId = batchManager.createTask();
        
        // 异步执行批量处理
        CompletableFuture.runAsync(() -> {
            try {
                processBatchRegenerate(taskId, queryParams);
            } catch (Exception e) {
                log.error("[batchRegenerateFailedWorkOrder][任务({})] 批量处理异常", taskId, e);
                batchManager.failTask(taskId, e.getMessage());
            }
        });
        
        return taskId;
    }

    /**
     * 处理批量重新生成失败的工单
     */
    private void processBatchRegenerate(String taskId, WandaWorkOrderPageReqVO reqVo) {
        // 只处理生成失败的工单
        reqVo.setStatus(2); // 生成失败

        // 1. 首先获取所有需要处理的工单ID
        List<Long> recordIds = new ArrayList<>();
        int pageNo = 1;
        int pageSize = 200;
        reqVo.setPageSize(pageSize);

        while (true) {
            reqVo.setPageNo(pageNo++);
            PageResult<WandaWorkOrderDO> pageResult = wandaWorkOrderMapper.selectPage(reqVo);
            if (pageResult.getList().isEmpty()) {
                break;
            }
            for (WandaWorkOrderDO item : pageResult.getList()) {
                recordIds.add(item.getId());
            }
            if (pageResult.getList().size() < pageSize) {
                break;
            }
        }

        int totalCount = recordIds.size();
        if (totalCount == 0) {
            batchManager.updateTotalCount(taskId, 0);
            batchManager.completeTask(taskId);
            return;
        }

        batchManager.updateTotalCount(taskId, totalCount);

        // 使用线程池并行处理
        int processors = Runtime.getRuntime().availableProcessors();
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                processors, // 核心线程数
                processors * 2, // 最大线程数
                60, TimeUnit.SECONDS, // 线程空闲时间
                new LinkedBlockingQueue<>(totalCount), // 工作队列
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );

        try {
            // 2. 遍历ID列表，提交处理任务
            for (Long recordId : recordIds) {
                executor.execute(() -> {
                    WandaWorkOrderDO record = wandaWorkOrderMapper.selectById(recordId);
                    // 增加一次检查，确保记录存在且状态仍为生成失败
                    if (record != null && record.getStatus() == 2) {
                        processRecord(taskId, record);
                    }
                });
            }

            // 3. 等待所有任务完成
            executor.shutdown();
            if (!executor.awaitTermination(1, TimeUnit.HOURS)) {
                log.warn("[processBatchRegenerate][任务({})] 等待任务完成超时", taskId);
                executor.shutdownNow();
            }

            // 4. 任务全部完成
            batchManager.completeTask(taskId);
        } catch (Exception e) {
            log.error("[processBatchRegenerate][任务({})] 处理异常", taskId, e);
            batchManager.failTask(taskId, e.getMessage());
            // 尝试关闭线程池
            executor.shutdownNow();
        }
    }

    /**
     * 处理单条记录
     *
     * @param taskId 任务ID
     * @param record 记录
     */
    private void processRecord(String taskId, WandaWorkOrderDO record) {
        // 初始化处理结果
        WandaWorkOrderBatchManager.ProcessResult result = new WandaWorkOrderBatchManager.ProcessResult(
                record.getId(),
                record.getHospitalCode(),
                record.getHospitalName(),
                record.getTreatmentSerialNumber(),
                record.getName(),
                record.getIdCardNumber(),
                record.getOrderType(),
                false, // 默认失败
                null,
                new Date()
        );
        
        try {
            // 调用生成工单方法，try-catch确保即使有异常也不影响结果处理
            try {
                generateWorkOrder(record.getId());
                // 设置成功
                result.setSuccess(true);
            } catch (Exception e) {
                // 获取失败原因
                result.setFailReason(e.getMessage());
                // 状态已在generateWorkOrder中通过新事务更新，这里无需再次更新
            }
        } catch (Exception e) {
            log.error("[processRecord][任务({}) 处理记录({})异常]", taskId, record.getId(), e);
            result.setFailReason("处理异常: " + e.getMessage());
        } finally {
            // 添加处理结果
            batchManager.addResult(taskId, result);
        }
    }

    @Override
    public WandaWorkOrderBatchProgressVO getBatchGenerateProgress(String taskId) {
        WandaWorkOrderBatchProgressVO progress = batchManager.getProgress(taskId);
        if (progress == null) {
            throw new ServiceException(500, "批量任务不存在");
        }
        return progress;
    }

    /**
     * 检查授权记录
     * @param idCardNumber 身份证号
     * @return 是否有授权记录
     */
    private boolean checkAuthorization(String idCardNumber) {
        return insuranceAuthService.getAuthByIdcard(idCardNumber) != null;
    }
    
    /**
     * 根据失败原因判断失败类型
     * @param failReason 失败原因
     * @return 失败类型
     */
    private Integer determineFailType(String failReason, String idCardNumber) {
        // 根据具体失败原因判断类型
        if (failReason == null) {
            return 0; // 未知错误
        }
        
        if (failReason.contains("患者未授权")) {
            return 1; // 未授权
        }
        
        if (failReason.contains("工单异常") || failReason.contains("Exception") || 
            failReason.contains("Error") || failReason.contains("异常")) {
            return 2; // 工单异常
        }
        
        if (failReason.contains("诊断编码不在意外险ICD表内")) {
            return 3; // 诊断编码不在意外险ICD表内
        }
        
        if (failReason.contains("患者年龄小于59岁") || failReason.contains("年龄小于60岁")) {
            return 4; // 患者年龄小于59岁，不符合老年人意外险条件
        }
        
        if (failReason.contains("门诊不支持残疾人险") || failReason.contains("门诊只支持老年人意外险")) {
            return 5; // 门诊权限不符
        }
        
        if (failReason.contains("门诊记录对应患者非本地户口")) {
            return 6; // 门诊非本地户口
        }
        
        if (failReason.contains("门诊记录对应患者所在区域无负责保险公司")) {
            return 7; // 门诊区域无保险公司
        }
        
        if (failReason.contains("门诊记录对应患者找不到区域")) {
            return 8; // 门诊找不到区域
        }
        
        if (failReason.contains("住院记录对应患者非本地户口")) {
            return 6; // 住院非本地户口
        }
        
        if (failReason.contains("住院记录对应患者所在区域无负责保险公司")) {
            return 7; // 住院区域无保险公司
        }
        
        if (failReason.contains("住院记录对应患者找不到区域")) {
            return 8; // 住院找不到区域
        }
        
        if (failReason.contains("已存在相同工单")) {
            return 9; // 已存在相同工单
        }
        
        return 0; // 默认未知错误
    }

    /**
     * 使用新事务更新工单状态
     */
    private void updateWorkOrderStatusWithNewTransaction(Long id, boolean success, String failReason) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus status = null;
        
        try {
            status = transactionManager.getTransaction(def);
            
            // 查询最新记录
            WandaWorkOrderDO workOrder = wandaWorkOrderMapper.selectById(id);
            if (workOrder != null) {
                if (success) {
                    workOrder.setStatus(1); // 已生成
                    workOrder.setFailReason(null);
                    workOrder.setFailType(null);
                } else {
                    workOrder.setStatus(2); // 生成失败
                    String finalFailReason = failReason;
                    if (failReason != null && failReason.length() > 500) {
                        finalFailReason = failReason.substring(0, 500); // 限制长度
                    }
                    workOrder.setFailReason(finalFailReason);
                    
                    // 设置失败类型
                    Integer failType = determineFailType(finalFailReason, workOrder.getIdCardNumber());
                    workOrder.setFailType(failType);
                }
                wandaWorkOrderMapper.updateById(workOrder);
            }
            
            transactionManager.commit(status);
        } catch (Exception e) {
            if (status != null) {
                transactionManager.rollback(status);
            }
            log.error("更新工单状态异常, id: {}, 错误: {}", id, e.getMessage(), e);
        }
    }

    @Override
    public List<WandaWorkOrderBatchProgressVO> getUnfinishedBatchTasks() {
        return batchManager.getUnfinishedTasks();
    }

    @Override
    public String batchSupplementElectronicBill() {
        // 检查是否有未完成的任务
        List<WandaWorkOrderBatchProgressVO> unfinishedTasks = batchManager.getUnfinishedTasks();
        if (!unfinishedTasks.isEmpty()) {
            throw new ServiceException(500, "存在未完成的批量任务,请等待当前任务完成后再试");
        }

        // 创建批处理任务
        String taskId = batchManager.createTask();

        // 异步执行批量处理
        CompletableFuture.runAsync(() -> {
            try {
                processBatchSupplementElectronicBill(taskId);
            } catch (Exception e) {
                log.error("[batchSupplementElectronicBill][任务({})] 批量补充电子票据异常", taskId, e);
                batchManager.failTask(taskId, e.getMessage());
            }
        });

        return taskId;
    }

    @Override
    public WandaWorkOrderBatchProgressVO getBatchSupplementProgress(String taskId) {
        WandaWorkOrderBatchProgressVO progress = batchManager.getProgress(taskId);
        if (progress == null) {
            throw new ServiceException(500, "批量任务不存在");
        }
        return progress;
    }

    /**
     * 处理批量补充电子票据信息
     *
     * @param taskId 任务ID
     */
    private void processBatchSupplementElectronicBill(String taskId) {
        // 查询符合条件的工单：状态是待接单(0)且票据完整度为无数据(2)或不确定(1)
        WandaWorkOrderPageReqVO queryParams = new WandaWorkOrderPageReqVO();
        queryParams.setStatus(0); // 待接单

        // 1. 首先获取所有需要处理的工单ID
        List<Long> recordIds = new ArrayList<>();
        int pageNo = 1;
        int pageSize = 200;
        queryParams.setPageSize(pageSize);

        while (true) {
            queryParams.setPageNo(pageNo++);
            PageResult<WandaWorkOrderDO> pageResult = wandaWorkOrderMapper.selectPage(queryParams);
            if (pageResult.getList().isEmpty()) {
                break;
            }

            // 筛选票据完整度为无数据或不确定的工单
            for (WandaWorkOrderDO item : pageResult.getList()) {
                // 检查是否存在对应的正式工单
                boolean existsWorkOrder = workOrderService.existsWorkOrder(
                    item.getIdCardNumber(),
                    item.getHospitalCode(),
                    item.getTreatmentSerialNumber(),
                    item.getTreatmentSerialNumberType()
                );

                if (existsWorkOrder) {
                    // 获取对应的正式工单
                    WorkOrder2DO workOrder2 = workOrderService.getWorkOrderByKey(
                        item.getIdCardNumber(),
                        item.getHospitalCode(),
                        item.getTreatmentSerialNumber(),
                        item.getTreatmentSerialNumberType()
                    );

                    // 检查正式工单状态和票据完整度
                    if (workOrder2 != null &&
                        workOrder2.getStatus() == 0 && // 正式工单也必须是待接单状态
                        (WorkOrderTicketCompleteStatusEnum.UNSURE.getStatus().equals(workOrder2.getCompleteStatus()) ||
                         WorkOrderTicketCompleteStatusEnum.NO_DATA.getStatus().equals(workOrder2.getCompleteStatus()))) {
                        recordIds.add(item.getId());
                    }
                }
            }

            if (pageResult.getList().size() < pageSize) {
                break;
            }
        }

        if (recordIds.isEmpty()) {
            batchManager.completeTask(taskId);
            return;
        }

        // 2. 设置总数
        batchManager.updateTotalCount(taskId, recordIds.size());

        // 3. 创建线程池处理
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                5, 10, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(100),
                r -> new Thread(r, "supplement-electronic-bill-" + System.currentTimeMillis()),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        try {
            // 4. 遍历ID列表，提交处理任务
            for (Long recordId : recordIds) {
                executor.execute(() -> {
                    WandaWorkOrderDO record = wandaWorkOrderMapper.selectById(recordId);
                    if (record != null && record.getStatus() == 0) {
                        processSupplementRecord(taskId, record);
                    }
                });
            }

            // 5. 等待所有任务完成
            executor.shutdown();
            if (!executor.awaitTermination(1, TimeUnit.HOURS)) {
                log.warn("[processBatchSupplementElectronicBill][任务({})] 等待任务完成超时", taskId);
                executor.shutdownNow();
            }

            // 6. 任务全部完成
            batchManager.completeTask(taskId);
        } catch (Exception e) {
            log.error("[processBatchSupplementElectronicBill][任务({})] 处理异常", taskId, e);
            batchManager.failTask(taskId, e.getMessage());
            executor.shutdownNow();
        }
    }

    /**
     * 处理单条补充电子票据记录
     *
     * @param taskId 任务ID
     * @param record 记录
     */
    private void processSupplementRecord(String taskId, WandaWorkOrderDO record) {
        // 初始化处理结果
        WandaWorkOrderBatchManager.ProcessResult result = new WandaWorkOrderBatchManager.ProcessResult(
                record.getId(),
                record.getHospitalCode(),
                record.getHospitalName(),
                record.getTreatmentSerialNumber(),
                record.getName(),
                record.getIdCardNumber(),
                record.getOrderType(),
                false, // 默认失败
                null,
                new Date()
        );

        try {
            // 首先检查预处理工单状态是否为待接单
            if (record.getStatus() != 0) {
                result.setFailReason("工单状态不是待接单，跳过处理");
                result.setSuccess(true); // 虽然跳过，但算作成功
                return;
            }

            // 检查是否已经存在对应的正式工单
            boolean existsWorkOrder = workOrderService.existsWorkOrder(
                record.getIdCardNumber(),
                record.getHospitalCode(),
                record.getTreatmentSerialNumber(),
                record.getTreatmentSerialNumberType()
            );

            if (!existsWorkOrder) {
                result.setFailReason("未找到对应的正式工单");
                return;
            }

            // 获取对应的正式工单
            WorkOrder2DO workOrder2 = workOrderService.getWorkOrderByKey(
                record.getIdCardNumber(),
                record.getHospitalCode(),
                record.getTreatmentSerialNumber(),
                record.getTreatmentSerialNumberType()
            );

            if (workOrder2 == null) {
                result.setFailReason("未找到对应的正式工单");
                return;
            }

            // 检查正式工单状态是否为待接单
            if (workOrder2.getStatus() != 0) {
                result.setFailReason("正式工单状态不是待接单，跳过处理");
                result.setSuccess(true); // 虽然跳过，但算作成功
                return;
            }

            // 检查票据完整度是否为无数据或不确定
            if (!WorkOrderTicketCompleteStatusEnum.UNSURE.getStatus().equals(workOrder2.getCompleteStatus()) &&
                !WorkOrderTicketCompleteStatusEnum.NO_DATA.getStatus().equals(workOrder2.getCompleteStatus())) {
                result.setFailReason("工单票据完整度不符合补充条件");
                return;
            }

            // 保存原始电子票据号用于比对
            String originalElectronicBillIds = workOrder2.getElectronicBillIds();

            // 重新处理电子票据
            electronicBillService.workOrderHandler(workOrder2);

            // 检查电子票据号是否有变更
            String newElectronicBillIds = workOrder2.getElectronicBillIds();
            boolean hasChanged = !Objects.equals(originalElectronicBillIds, newElectronicBillIds);

            if (!hasChanged) {
                result.setFailReason("电子票据号无变更，跳过处理");
                result.setSuccess(true); // 虽然跳过，但算作成功
                return;
            }

            // 重新计算赔付金额
            InsuranceTypeEnum insuranceTypeEnum = InsuranceTypeEnum.getByCode(workOrder2.getType());
            BasicEstimateHandler estimateHandler = applicationContext.getBean(insuranceTypeEnum.getEstimateHandlerClazz());

            // 获取区域和保险公司信息
            AreaDO area = areaService.getArea(workOrder2.getAreaId());
            AreaCompanyDo areaCompany = areaService.getResponsibleCompany(area.getId(), workOrder2.getTreatmentDatetime(), insuranceTypeEnum);

            EstimateResult estimateResult = estimateHandler.apply(new EstimateParam(workOrder2,
                CompensatingOption.DEFAULT_COMPENSATING_OPTION.updateInsuranceBeginDate(areaCompany.getAgreementDate())));
            SuggestCompensatingMoney suggestCompensatingMoney = estimateResult.getSuggestCompensatingMoney();

            workOrder2.setMedicalCosts(suggestCompensatingMoney.getMedicalCosts());
            workOrder2.setHospitalizationAllowance(suggestCompensatingMoney.getHospitalizationAllowance());
            workOrder2.setDiagnosisAllowance(suggestCompensatingMoney.getDiagnosisAllowance());
            workOrder2.setSuggestCompensatingMoney(suggestCompensatingMoney.getSum());

            // 更新工单
            workOrderService.updateWorkOrder2(workOrder2);

            // 重新生成PDF（删除旧的，生成新的）
            workOrderService.buildUnsignedPdf(workOrder2);

            result.setSuccess(true);

        } catch (Exception e) {
            log.error("[processSupplementRecord][任务({}) 处理记录({})异常]", taskId, record.getId(), e);
            result.setFailReason("处理异常: " + e.getMessage());
        } finally {
            // 添加处理结果
            batchManager.addResult(taskId, result);
        }
    }

    /**
     * 检查是否存在相同工单
     *
     * @param idCardNumber 身份证号
     * @param hospitalCode 医院代码
     * @param treatmentSerialNumber 就诊流水号
     * @param treatmentSerialNumberType 门急诊类型（0门诊，1住院）
     * @return 是否存在
     */
    private boolean existsSameWorkOrder(String idCardNumber, String hospitalCode, String treatmentSerialNumber, Integer treatmentSerialNumberType) {
        return workOrderService.existsWorkOrder(idCardNumber, hospitalCode, treatmentSerialNumber, treatmentSerialNumberType);
    }
}
