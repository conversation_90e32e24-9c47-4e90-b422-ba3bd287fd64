{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\eslint-loader@2.2.1_eslint@7.15.0_webpack@4.46.0\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\api\\insurance\\wandaWorkOrder.js", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\api\\insurance\\wandaWorkOrder.js", "mtime": 1753858447584}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\babel.config.js", "mtime": 1691889630125}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1667694382043}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\eslint-loader@2.2.1_eslint@7.15.0_webpack@4.46.0\\node_modules\\eslint-loader\\index.js", "mtime": 1667694382021}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["createWandaWorkOrder", "data", "request", "url", "method", "updateWandaWorkOrder", "deleteWandaWorkOrder", "id", "getWandaWorkOrder", "getWandaWorkOrderPage", "query", "params", "exportWandaWorkOrderExcel", "responseType", "startSync", "getSyncProgress", "taskId", "resumeSync", "getUnfinishedTask", "getWandaWorkOrderStats", "generateWorkOrder", "getUnfinishedTaskList", "checkTaskLock", "batchGenerateWorkOrder", "getBatchGenerateProgress", "downloadBatchGenerateReport", "batchRegenerateFailedWorkOrder", "getUnfinishedBatchTasks", "batchSupplementElectronicBill", "getBatchSupplementProgress"], "sources": ["C:/projects/shenlan/insurance/yudao-ui-admin/src/api/insurance/wandaWorkOrder.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 创建预处理工单\r\nexport function createWandaWorkOrder(data) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/create',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 更新预处理工单\r\nexport function updateWandaWorkOrder(data) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/update',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除预处理工单\r\nexport function deleteWandaWorkOrder(id) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/delete?id=' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 获得预处理工单\r\nexport function getWandaWorkOrder(id) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/get?id=' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 获得预处理工单分页\r\nexport function getWandaWorkOrderPage(query) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/page',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 导出预处理工单 Excel\r\nexport function exportWandaWorkOrderExcel(query) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/export-excel',\r\n    method: 'get',\r\n    params: query,\r\n    responseType: 'blob'\r\n  })\r\n}\r\n\r\n// 开始同步\r\nexport function startSync(data) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/sync',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 获取同步进度\r\nexport function getSyncProgress(taskId) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/sync/progress/' + taskId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 继续同步\r\nexport function resumeSync(taskId) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/sync/resume/' + taskId,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 获取未完成的同步任务\r\nexport function getUnfinishedTask() {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/sync/unfinished',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 获取字段完整统计\r\nexport function getWandaWorkOrderStats() {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/stats',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 生成工单\r\nexport function generateWorkOrder(id) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/generate/' + id,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 获取所有未完成的同步任务列表\r\nexport function getUnfinishedTaskList() {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/sync/unfinished/list',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 检查任务是否被锁定\r\nexport function checkTaskLock(taskId) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/sync/check-lock/' + taskId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 批量生成工单\r\nexport function batchGenerateWorkOrder(data) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/batch-generate',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 获取批量生成进度\r\nexport function getBatchGenerateProgress(taskId) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/batch-generate/progress/' + taskId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 下载批量生成报告\r\nexport function downloadBatchGenerateReport(taskId) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/batch-generate/download/' + taskId,\r\n    method: 'get',\r\n    responseType: 'blob'\r\n  })\r\n}\r\n\r\n// 批量重新生成失败工单\r\nexport function batchRegenerateFailedWorkOrder(data) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/batch-regenerate',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 获取未完成的批量生成任务\r\nexport function getUnfinishedBatchTasks() {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/batch-generate/unfinished',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 批量补充电子票据信息\r\nexport function batchSupplementElectronicBill() {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/batch-supplement-electronic-bill',\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 获取批量补充电子票据信息进度\r\nexport function getBatchSupplementProgress(taskId) {\r\n  return request({\r\n    url: '/insurance/wanda-work-order/batch-supplement-electronic-bill/progress/' + taskId,\r\n    method: 'get'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA;AACO,SAASA,oBAAT,CAA8BC,IAA9B,EAAoC;EACzC,OAAO,IAAAC,gBAAA,EAAQ;IACbC,GAAG,EAAE,oCADQ;IAEbC,MAAM,EAAE,MAFK;IAGbH,IAAI,EAAEA;EAHO,CAAR,CAAP;AAKD,C,CAED;;;AACO,SAASI,oBAAT,CAA8BJ,IAA9B,EAAoC;EACzC,OAAO,IAAAC,gBAAA,EAAQ;IACbC,GAAG,EAAE,oCADQ;IAEbC,MAAM,EAAE,KAFK;IAGbH,IAAI,EAAEA;EAHO,CAAR,CAAP;AAKD,C,CAED;;;AACO,SAASK,oBAAT,CAA8BC,EAA9B,EAAkC;EACvC,OAAO,IAAAL,gBAAA,EAAQ;IACbC,GAAG,EAAE,2CAA2CI,EADnC;IAEbH,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASI,iBAAT,CAA2BD,EAA3B,EAA+B;EACpC,OAAO,IAAAL,gBAAA,EAAQ;IACbC,GAAG,EAAE,wCAAwCI,EADhC;IAEbH,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASK,qBAAT,CAA+BC,KAA/B,EAAsC;EAC3C,OAAO,IAAAR,gBAAA,EAAQ;IACbC,GAAG,EAAE,kCADQ;IAEbC,MAAM,EAAE,KAFK;IAGbO,MAAM,EAAED;EAHK,CAAR,CAAP;AAKD,C,CAED;;;AACO,SAASE,yBAAT,CAAmCF,KAAnC,EAA0C;EAC/C,OAAO,IAAAR,gBAAA,EAAQ;IACbC,GAAG,EAAE,0CADQ;IAEbC,MAAM,EAAE,KAFK;IAGbO,MAAM,EAAED,KAHK;IAIbG,YAAY,EAAE;EAJD,CAAR,CAAP;AAMD,C,CAED;;;AACO,SAASC,SAAT,CAAmBb,IAAnB,EAAyB;EAC9B,OAAO,IAAAC,gBAAA,EAAQ;IACbC,GAAG,EAAE,kCADQ;IAEbC,MAAM,EAAE,MAFK;IAGbH,IAAI,EAAEA;EAHO,CAAR,CAAP;AAKD,C,CAED;;;AACO,SAASc,eAAT,CAAyBC,MAAzB,EAAiC;EACtC,OAAO,IAAAd,gBAAA,EAAQ;IACbC,GAAG,EAAE,+CAA+Ca,MADvC;IAEbZ,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASa,UAAT,CAAoBD,MAApB,EAA4B;EACjC,OAAO,IAAAd,gBAAA,EAAQ;IACbC,GAAG,EAAE,6CAA6Ca,MADrC;IAEbZ,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASc,iBAAT,GAA6B;EAClC,OAAO,IAAAhB,gBAAA,EAAQ;IACbC,GAAG,EAAE,6CADQ;IAEbC,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASe,sBAAT,GAAkC;EACvC,OAAO,IAAAjB,gBAAA,EAAQ;IACbC,GAAG,EAAE,mCADQ;IAEbC,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASgB,iBAAT,CAA2Bb,EAA3B,EAA+B;EACpC,OAAO,IAAAL,gBAAA,EAAQ;IACbC,GAAG,EAAE,0CAA0CI,EADlC;IAEbH,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASiB,qBAAT,GAAiC;EACtC,OAAO,IAAAnB,gBAAA,EAAQ;IACbC,GAAG,EAAE,kDADQ;IAEbC,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASkB,aAAT,CAAuBN,MAAvB,EAA+B;EACpC,OAAO,IAAAd,gBAAA,EAAQ;IACbC,GAAG,EAAE,iDAAiDa,MADzC;IAEbZ,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASmB,sBAAT,CAAgCtB,IAAhC,EAAsC;EAC3C,OAAO,IAAAC,gBAAA,EAAQ;IACbC,GAAG,EAAE,4CADQ;IAEbC,MAAM,EAAE,MAFK;IAGbH,IAAI,EAAEA;EAHO,CAAR,CAAP;AAKD,C,CAED;;;AACO,SAASuB,wBAAT,CAAkCR,MAAlC,EAA0C;EAC/C,OAAO,IAAAd,gBAAA,EAAQ;IACbC,GAAG,EAAE,yDAAyDa,MADjD;IAEbZ,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASqB,2BAAT,CAAqCT,MAArC,EAA6C;EAClD,OAAO,IAAAd,gBAAA,EAAQ;IACbC,GAAG,EAAE,yDAAyDa,MADjD;IAEbZ,MAAM,EAAE,KAFK;IAGbS,YAAY,EAAE;EAHD,CAAR,CAAP;AAKD,C,CAED;;;AACO,SAASa,8BAAT,CAAwCzB,IAAxC,EAA8C;EACnD,OAAO,IAAAC,gBAAA,EAAQ;IACbC,GAAG,EAAE,8CADQ;IAEbC,MAAM,EAAE,MAFK;IAGbH,IAAI,EAAEA;EAHO,CAAR,CAAP;AAKD,C,CAED;;;AACO,SAAS0B,uBAAT,GAAmC;EACxC,OAAO,IAAAzB,gBAAA,EAAQ;IACbC,GAAG,EAAE,uDADQ;IAEbC,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASwB,6BAAT,GAAyC;EAC9C,OAAO,IAAA1B,gBAAA,EAAQ;IACbC,GAAG,EAAE,8DADQ;IAEbC,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASyB,0BAAT,CAAoCb,MAApC,EAA4C;EACjD,OAAO,IAAAd,gBAAA,EAAQ;IACbC,GAAG,EAAE,2EAA2Ea,MADnE;IAEbZ,MAAM,EAAE;EAFK,CAAR,CAAP;AAID"}]}