{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\claimsStatisticsMoney.vue?vue&type=style&index=0&id=30bbfb75&scoped=true&lang=css&", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\claimsStatisticsMoney.vue", "mtime": 1753840095022}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\css-loader@3.6.0_webpack@4.46.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1667694382297}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1667694382639}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1667694382634}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogIHBhZGRpbmc6IDIwcHg7DQp9DQo="}, {"version": 3, "sources": ["claimsStatisticsMoney.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DA;AACA;AACA", "file": "claimsStatisticsMoney.vue", "sourceRoot": "src/views/insurance/workOrder", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"90px\">\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker v-model=\"createDateRange\" style=\"width: 240px\" value-format=\"yyyy-MM-dd\"\r\n                        type=\"daterange\" range-separator=\"-\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-table v-loading=\"loading\" :data=\"claimsData\">\r\n      <el-table-column label=\"公司名称\" prop=\"companyName\" />\r\n      <el-table-column label=\"门急诊金额\" prop=\"outpatientMoney\" />\r\n      <el-table-column label=\"住院金额\" prop=\"inpatientMoney\" />\r\n      <el-table-column label=\"金额\" prop=\"money\" />\r\n    </el-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getWorkOrderStat } from '@/api/insurance/workOrder';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      claimsData: [],\r\n      loading: true,\r\n      createDateRange: [],\r\n      queryParams: {}\r\n    };\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      this.loading = true;\r\n      let params = {...this.queryParams};\r\n      this.addBeginAndEndTime(params, this.createDateRange, 'createTime');\r\n      getWorkOrderStat(params).then(response => {\r\n        this.claimsData = response.data;\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handleQuery() {\r\n      this.fetchData();\r\n    },\r\n    resetQuery() {\r\n      this.createDateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchData();\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n</style> \r\n"]}]}